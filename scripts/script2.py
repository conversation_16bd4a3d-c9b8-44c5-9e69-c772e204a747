# -*- coding: utf-8 -*-
"""
Spyder Editor

This is a temporary script file.
"""
import os 
from os import path
from PIL import Image
import re
from skimage import io
import shutil
from PIL import Image, ImageChops
Image.MAX_IMAGE_PIXELS = 10000000000
import numpy as np

imagePath = r"D:\projects\wujindeyuansushi\gamecoredemo\src\main\res\drawable-xhdpi"

def corp_margin(imgIn, fileRealPath):
 # 创建一个边框颜色图片
    bg = Image.new(imgIn.mode, imgIn.size, imgIn.getpixel((0, 0)))
    diff = ImageChops.difference(imgIn, bg)
    # diff = ImageChops.add(diff, diff, 2.0, -10) # 可选，会去的更干净，副作用是误伤
    bbox = diff.getbbox()   # 返回左上角和右下角的坐标 (left, upper, right, lower)
    if bbox:
        print(fileName)
        imgIn.crop(bbox).save(fileRealPath, quality=95)

for parent, _, fileNames in os.walk(imagePath):
   for fileName in fileNames:
       realFilePath = path.join(imagePath, fileName)
       im = io.imread(realFilePath)
       # if (re.match(r"role_\d+", fileName) != None):
       #      print(fileName)
       #      print(re.match(r"role_\d+", fileName).group())

       if (re.match(r"role_\d+", fileName) != None 
            and re.match(r"role_\d+", fileName).group() == fileName.split(".")[0]):
            im = corp_margin(im, realFilePath)