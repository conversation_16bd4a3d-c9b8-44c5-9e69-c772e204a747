package com.moyu.core.config

import com.moyu.core.model.Difficult

class DifficultConfigParser : ConfigParser<Difficult> {
    override fun parse(line: String): Difficult {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val attribute1 = words[i++].trim().toInt()
        val attribute2 = words[i++].trim().toInt()
        val attribute3 = words[i++].trim().toInt()
        val attribute4 = words[i++].trim().toInt()
        val attribute5 = words[i++].trim().toInt()
        val attribute6 = words[i++].trim().toInt()
        val attribute7 = words[i++].trim().toInt()
        val attribute8 = words[i++].trim().toDouble()
        val attribute9 = words[i++].trim().toDouble()
        val attribute10 = words[i++].trim().toDouble()
        val attribute11 = words[i++].trim().toInt()
        val badgeType = words[i++].trim().toInt()
        val adv1 = words[i++].trim().toInt()
        val adv2 = words[i++].trim().toInt()
        val adv3 = words[i++].trim().toInt()
        val adv4 = words[i++].trim().toInt()
        val adv5 = words[i++].trim().toInt()
        val resource1 = words[i++].trim().toInt()
        val resource2 = words[i].trim().toInt()
        return Difficult(
            id,
            name,
            attribute1,
            attribute2,
            attribute3,
            attribute4,
            attribute5,
            attribute6,
            attribute7,
            attribute8,
            attribute9,
            attribute10,
            attribute11,
            badgeType,
            adv1,
            adv2,
            adv3,
            adv4,
            adv5,
            resource1,
            resource2
        )
    }
}