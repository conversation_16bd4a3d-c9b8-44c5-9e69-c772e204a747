package com.moyu.core.config

import com.moyu.core.model.Title

class TitleConfigParser : ConfigParser<Title> {
    override fun parse(line: String): Title {
        val words = line.split("\t")
        var i = 0
        val id = words[i++].trim().toInt()
        val name = words[i++].trim()
        val level = words[i++].trim().toInt()
        val conditionType = words[i++].trim().split(",").map { it.toInt() }
        val conditionNum = words[i++].trim().split(",").map { it.toInt() }
        val reward = words[i++].trim().toInt()
        val desc = ""//words[i].trim()
        return Title(
            id,
            name,
            level,
            conditionType,
            conditionNum,
            reward,
            desc
        )
    }
}