package com.moyu.core.model

import com.moyu.core.model.property.Property

data class Difficult(
    override val id: Int = 0,
    val name: String = "",
    val attribute1: Int = 0,
    val attribute2: Int = 0,
    val attribute3: Int = 0,
    val attribute4: Int = 0,
    val attribute5: Int = 0,
    val attribute6: Int = 0,
    val attribute7: Int = 0,
    val attribute8: Double = 0.0,
    val attribute9: Double = 0.0,
    val attribute10: Double = 0.0,
    val attribute11: Int = 0,
    val badgeType: Int = 0,
    val adv1: Int = 0,
    val adv2: Int = 0,
    val adv3: Int = 0,
    val adv4: Int = 0,
    val adv5: Int = 0,
    val resource1: Int = 0,
    val resource2: Int = 0,
): ConfigData {
    fun toProperty(): Property {
        val defense = listOf(attribute2, attribute3, attribute4, attribute5, attribute6)
        return Property(
            attack = attribute1,
            defenses = defense,
            hp = attribute7,
            fatalRate = attribute8,
            fatalDamage = attribute9,
            dodgeRate = attribute10,
            speed = attribute11
        )
    }
}