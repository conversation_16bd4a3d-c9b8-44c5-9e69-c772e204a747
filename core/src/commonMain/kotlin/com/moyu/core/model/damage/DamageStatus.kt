package com.moyu.core.model.damage

import com.moyu.core.AppWrapper
import core.generated.resources.*

data class DamageStatus(
    val isFatal: Boolean = false,
    val isRage: <PERSON>olean = false,
    val isDodge: <PERSON>olean = false,
    val isImmune: <PERSON>olean = false,
    val isHolyShield: <PERSON>olean = false,
    val isSelfHarm: Boolean = false
) {
    fun getDesc(): String {
        return when {
            isRage -> AppWrapper.getStringKmp(Res.string.rage)
            isImmune -> AppWrapper.getStringKmp(Res.string.unbreakable)
            isHolyShield -> AppWrapper.getStringKmp(Res.string.holy_shield)
            isDodge -> AppWrapper.getStringKmp(Res.string.dodge)
            isFatal -> AppWrapper.getStringKmp(Res.string.fatal)
            else -> ""
        }
    }

}