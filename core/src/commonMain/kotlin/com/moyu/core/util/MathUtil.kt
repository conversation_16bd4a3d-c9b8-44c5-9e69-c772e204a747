package com.moyu.core.util

import kotlin.math.roundToInt
import kotlin.random.Random

val RANDOM = Random(p_getTimeMillis())
fun Int.chance(): <PERSON><PERSON>an {
    return RANDOM.nextInt(0, 99) < this
}

/**
 *  数值范围在 0-100 之间 概率判断
 */
fun Double.chance(): <PERSON><PERSON><PERSON> {
    return RANDOM.nextInt(10000) < this * 100
}

/**
 *  数值范围在 0-1.0 之间 进行概率判断
 */
fun Double.chance100(): Bo<PERSON>an {
    return RANDOM.nextInt(10000) < this * 10000
}

fun <T> Iterable<T>.gameShuffled(): List<T> = toMutableList().apply { shuffle(RANDOM) }

/**
 * 把double转成小数点后一位的字符串，比如 0.51转0.5
 * 如果数值小于1，保留两位小数点，否则，1位
 */
fun Double.realValueToDotWithOneDigits(): String {
    return if (this >=1) ((this * 10).roundToInt().toFloat() / 10).toString() else ((this * 100).roundToInt().toFloat() / 100).toString()
}

fun Double.realValueToDotWithNoDigits(): String {
    return (this.roundToInt()).toString()
}

/**
 * 把double转成小数点后一位的字符串，比如 0.51转50
 * 如果数值小于1，保留两位小数点，否则，1位
 */
fun Double.percentValueToDot(): String {
    return ((this * 10000).roundToInt() / 100f).toString()
}

/**
 * 把double转成小数点后一位的字符串，比如0.5转50.0%
 */
fun Double.percentValueToDotWithOneDigits(): String {
    return ((this * 10000).roundToInt() / 100f).toString() + "%"
}

/**
 * 把double转成小数点后一位的字符串，比如0.5转50%
 */
fun Double.percentValueToDotWithNoDigits(): String {
    return (this * 100).roundToInt().toString() + "%"
}

fun Int.toThreeDigits(): String {
    return if (this >= 100) this.toString() else if (this>=10) "0${this}" else "00${this}"
}

/**
 * 包含end，闭区间[start, end]
 */
fun Random.nextIntClosure(start: Int, end: Int): Int {
    return this.nextInt(end - start + 1) + start
}

/**
 * 返回一个随机数，范围是闭区间 [start, end]
 */
fun Random.nextDoubleClosure(start: Double, end: Double): Double {
    return this.nextDouble() * (end - start) + start
}

/**
 * 將純整數字串縮寫為 K / M / W / E。
 *
 * - zhCN = true  → 使用「萬 / 億」（W / E）
 * - zhCN = false → 使用「K / M」
 * - 數值 < 1e5 直接返回原字串
 * - M / E 檔保留 2 位小數（去掉無用 0）
 *
 * 這段程式碼不依賴 java.text、String.format，可在 KMP 各平台執行。
 */
fun String.shrinkNumber(zhCN: Boolean): String {
    val m = Regex("""^\s*([+-]?)(\d+)\s*$""").matchEntire(this) ?: return this
    val sign = m.groupValues[1]
    val value = m.groupValues[2].toLong()

    if (value < 100_000) return this.trim()   // 小於 1e5 不縮寫

    // ===== 工具：將 Double (>=0) 轉成最多 2 位小數的字串 =====
    fun to2dec(num: Double): String {
        val scaled = (num * 100).roundToInt()   // 四捨五入到整數（保留 2 位）
        val intPart = scaled / 100
        val frac = scaled % 100
        return when {
            frac == 0 -> intPart.toString()
            frac % 10 == 0 -> "$intPart.${frac / 10}"   // 例如 1.20 → 1.2
            else -> "$intPart.${frac.toString().padStart(2, '0')}"
        }
    }
    // ========================================================

    return if (zhCN) {
        if (value >= 100_000_000) {                     // 億
            "$sign${to2dec(value / 100_000_000.0)}E"
        } else {                                        // 萬
            "$sign${value / 10_000}W"
        }
    } else {
        if (value >= 1_000_000) {                       // 百萬
            "$sign${to2dec(value / 1_000_000.0)}M"
        } else {                                        // 千
            "$sign${value / 1_000}K"
        }
    }
}