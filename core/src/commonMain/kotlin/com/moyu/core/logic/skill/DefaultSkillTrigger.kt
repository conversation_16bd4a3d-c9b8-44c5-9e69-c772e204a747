package com.moyu.core.logic.skill

import com.moyu.core.GameCore
import com.moyu.core.logic.battle.BattleField
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import com.moyu.core.util.chance
import kotlin.math.abs

enum class TriggerType(val value: Int) {
    PASSIVE(1), // 被动
    TRIGGER(2), // 触发
    ACTIVE(3), // 主动
    NORMAL_ATTACK(4), // 普攻
}

fun Int.canDoubleSkill(): Bo<PERSON>an {
    return this == TriggerType.ACTIVE.value || this == TriggerType.NORMAL_ATTACK.value
}

object DefaultSkillTrigger {
    fun trigger(
        skill: Skill, field: BattleField, skillOwner: Role, triggeringSkill: Skill?
    ): Bo<PERSON>an {
        skill.apply {
            val conditionTrigger = mutableListOf<Boolean>()
            val peer = field.getDirectPeer(skillOwner) ?: kotlin.run {
                return false
            }
            // triggeringSkill是触发链的原因，比如回合开始，造成伤害，释放了xx技能等
            // this这个Skill是正在被test的技能，测试是否可以被触发，skillOwner就是被测试技能的拥有者
            val isTriggerTheOwner =
                triggeringSkill?.ownerIdentifier?.playerId() == skillOwner.roleIdentifier.playerId()
            val isTriggerTheSameTeam =
                triggeringSkill?.ownerIdentifier?.isPlayerSide() == skillOwner.roleIdentifier.isPlayerSide()
            val isTriggerTheEnemyTeam = !isTriggerTheSameTeam
            val isTriggerTheSameTeamButMe = isTriggerTheSameTeam && !isTriggerTheOwner
            val isYourTurn = field.isOnesTurn(skillOwner)

            if (triggerType == TriggerType.ACTIVE.value) {
                if (!isYourTurn) return false
            }
            // 概率可以被技能影响，根据类型先获得最终的实际概率
            val realRate = skillOwner.getChance(this, triggerType, rate)
            // 如果没有roll中，则返回
            if (!realRate.chance() && !GameCore.instance.getDebugConfig().easySkill) return false

            // roll中了，还要看条件，如果有的话
            activeCondition.forEachIndexed { index, condition ->
                val conditionResult = when (abs(condition)) {
                    1 -> {
                        // 我方生命值小于等于N%（负数为大于）
                        val targetHp =
                            if (GameCore.instance.getDebugConfig().easySkill) skillOwner.getDefaultProperty().hp - 1 else skillOwner.getDefaultProperty().hp * activeConditionNum[index] / 100
                        !skillOwner.isDeath() && skillOwner.getCurrentProperty().hp <= targetHp
                    }

                    2 -> {
                        // 敌方生命值小于等于N%（负数为大于）
                        val targetHp =
                            if (GameCore.instance.getDebugConfig().easySkill) peer.getDefaultProperty().hp - 1 else peer.getDefaultProperty().hp * activeConditionNum[index] / 100
                        !peer.isDeath() && peer.getCurrentProperty().hp <= targetHp
                    }

                    3 -> {
                        // 自己单次受伤达到一定生命比例时
                        triggeringSkill?.isWounded() == true && field.getWoundedResults(
                            skillOwner
                        ).lastOrNull()?.let {
                            val targetHp =
                                if (GameCore.instance.getDebugConfig().easySkill) skillOwner.getDefaultProperty().hp - 1 else skillOwner.getDefaultProperty().hp * activeConditionNum[index] / 100
                            !skillOwner.isDeath() && it.damageValue.finalDamage >= targetHp
                        } ?: false
                    }

                    4 -> {
                        // 任意敌方单次受伤达到一定生命比例时
                        if (triggeringSkill?.isWounded() == true && isTriggerTheEnemyTeam) {
                            val targetHp =
                                if (GameCore.instance.getDebugConfig().easySkill) skillOwner.getDefaultProperty().hp - 1 else skillOwner.getDefaultProperty().hp * activeConditionNum[index] / 100
                            (triggeringSkill.damageResult?.damageValue?.finalDamage
                                ?: 0) >= targetHp
                        } else false
                    }

                    11 -> {
                        // 我方有某些具体buff时
                        skillOwner.getShowBothBuff()
                            .find { it.id == activeConditionNum[index] } != null
                    }

                    12 -> {
                        // 任意敌方有某些具体buff时
                        field.getPeerTeamRoles(skillOwner).any {
                            it.getShowBothBuff()
                                .find { it.id == activeConditionNum[index] } != null
                        }
                    }

                    13 -> {
                        // 我方有任意debuff时
                        field.getMyTeamRoles(skillOwner).any {
                            it.getShowBadBuff().isNotEmpty()
                        }
                    }

                    14 -> {
                        // 敌方目标有某些具体buff时
                        peer.getShowBothBuff()
                            .find { it.id == activeConditionNum[index] } != null
                    }

                    15 -> {
                        // 全体敌方每个人都有某些具体buff时（不包括召唤物）
                        field.getPeerTeamRoles(skillOwner).filter { !it.isMinion() }.all {
                            it.getShowBadBuff().any { buff ->
                                buff.id == activeConditionNum[index]
                            }
                        }
                    }

                    21 -> {
                        // 自己第几回合
                        triggeringSkill?.isTurnBegin() == true && isTriggerTheOwner && field.getTurn() == activeConditionNum[index]
                    }

                    22 -> {
                        // 自己N回合后
                        triggeringSkill?.isTurnBegin() == true && isTriggerTheOwner && field.getTurn() >= activeConditionNum[index]
                    }

                    23 -> {
                        // 自己从第1回合开始每N个回合，注意N==1时候退化成自己回合开始
                        triggeringSkill?.isTurnBegin() == true && isTriggerTheOwner && (activeConditionNum[index] == 1 || field.getTurn() % activeConditionNum[index] == 1)
                    }

                    24 -> {
                        // 自己从第N回合开始每N个回合，注意N==1时候退化
                        triggeringSkill?.isTurnBegin() == true && isTriggerTheOwner && ((activeConditionNum[index] == 1 && field.getTurn() >= activeConditionNum[index]) || field.getTurn() % activeConditionNum[index] == 0)
                    }

                    25 -> {
                        // 自己从第1回合开始每N个回合，注意N==1时候退化成自己回合开始 状态
                        isTriggerTheOwner && (activeConditionNum[index] == 1 || field.getTurn() % activeConditionNum[index] == 1)
                    }

                    26 -> {
                        // 自己从第N回合开始每N个回合，注意N==1时候退化 状态
                        isTriggerTheOwner && ((activeConditionNum[index] == 1 && field.getTurn() >= activeConditionNum[index]) || field.getTurn() % activeConditionNum[index] == 0)
                    }

                    27 -> {
                        // 自己第N回合结束时
                        triggeringSkill?.isTurnEnd() == true && isTriggerTheOwner && field.getTurn() == activeConditionNum[index]
                    }

                    28 -> {
                        // 自己N回合后结束时
                        triggeringSkill?.isTurnEnd() == true && isTriggerTheOwner && field.getTurn() >= activeConditionNum[index]
                    }

                    29 -> {
                        // 自己从第1回合开始每N个回合结束时
                        triggeringSkill?.isTurnEnd() == true && isTriggerTheOwner && (activeConditionNum[index] == 1 || field.getTurn() % activeConditionNum[index] == 1)
                    }

                    30 -> {
                        // 自己从第N回合开始每N个回合结束时
                        triggeringSkill?.isTurnEnd() == true && isTriggerTheOwner && ((activeConditionNum[index] == 1 && field.getTurn() >= activeConditionNum[index]) || field.getTurn() % activeConditionNum[index] == 0)
                    }

                    31 -> {
                        //任意一个敌方的回合开始时
                        triggeringSkill?.isTurnBegin() == true && isTriggerTheEnemyTeam
                    }

                    32 -> {
                        //任意一个友方的回合开始时（不包括自己）
                        triggeringSkill?.isTurnBegin() == true && isTriggerTheSameTeamButMe
                    }

                    41 -> {
                        // 自己死亡时
                        triggeringSkill?.isDeath() == true && isTriggerTheOwner
                    }

                    42 -> {
                        // 任意一个敌方死亡时
                        triggeringSkill?.isDeath() == true && isTriggerTheEnemyTeam
                    }

                    43 -> {
                        // 任意一个友方死亡时（不包括自己）
                        triggeringSkill?.isDeath() == true && isTriggerTheSameTeamButMe
                    }

                    44 -> {
                        // 任意一个角色死亡时
                        triggeringSkill?.isDeath() == true
                    }

                    45 -> {
                        // 所有敌方角色死亡时
                        triggeringSkill?.isDeath() == true && field.getPeerTeamRoles(skillOwner)
                            .all { it.isDeath() }
                    }

                    46 -> {
                        // 任意一个敌方召唤物死亡时
                        triggeringSkill?.isDeath() == true && isTriggerTheEnemyTeam && triggeringSkill.ownerIdentifier.isMinion()
                    }

                    47 -> {
                        // 任意一个友方召唤物死亡时
                        triggeringSkill?.isDeath() == true && isTriggerTheSameTeam && triggeringSkill.ownerIdentifier.isMinion()
                    }

                    48 -> {
                        // 任意一个召唤物死亡时
                        triggeringSkill?.isDeath() == true && triggeringSkill.ownerIdentifier.isMinion()
                    }

                    51 -> {
                        // 自己成功格挡时
                        isTriggerTheOwner && triggeringSkill?.damageResult?.damageStatus?.isDodge == true
                    }

                    52 -> {
                        //任意敌方成功格挡时
                        isTriggerTheEnemyTeam && triggeringSkill?.damageResult?.damageStatus?.isDodge == true
                    }

                    53 -> {
                        //任意友方成功格挡时（不包括自己）
                        isTriggerTheSameTeamButMe && triggeringSkill?.damageResult?.damageStatus?.isDodge == true
                    }

                    54 -> {
                        //自己成功暴击
                        isTriggerTheOwner && triggeringSkill?.damageResult?.damageStatus?.isFatal == true
                    }

                    55 -> {
                        //任意敌方成功暴击时
                        isTriggerTheEnemyTeam && triggeringSkill?.damageResult?.damageStatus?.isFatal == true
                    }

                    56 -> {
                        //任意友方成功暴击时（不包括自己）
                        isTriggerTheSameTeamButMe && triggeringSkill?.damageResult?.damageStatus?.isFatal == true
                    }

                    57 -> {
                        //自己恢复生命值时
                        triggeringSkill?.isHealing() == true && isTriggerTheOwner
                    }

                    58 -> {
                        //任意敌方恢复生命值时
                        triggeringSkill?.isHealing() == true && isTriggerTheEnemyTeam
                    }

                    59 -> {
                        //任意敌方恢复生命值时
                        triggeringSkill?.isHealing() == true && isTriggerTheSameTeamButMe
                    }

                    60 -> {
                        // 自己成功吸血时
                        isTriggerTheOwner && triggeringSkill?.healResult?.healStatus?.isSuckBlood == true
                    }

                    61 -> {
                        // 任意敌方成功吸血时
                        isTriggerTheEnemyTeam && triggeringSkill?.healResult?.healStatus?.isSuckBlood == true
                    }

                    62 -> {
                        // 任意友方吸血时（不包括自己）
                        isTriggerTheSameTeamButMe && triggeringSkill?.healResult?.healStatus?.isSuckBlood == true
                    }

                    63 -> {
                        // 自己获得正面效果时
                        triggeringSkill?.isGettingBuff() == true && isTriggerTheOwner
                    }

                    64 -> {
                        // 任意敌方获得正面效果时
                        triggeringSkill?.isGettingBuff() == true && isTriggerTheEnemyTeam
                    }

                    65 -> {
                        // 任意友方获得正面效果时（不包括自己）
                        triggeringSkill?.isGettingBuff() == true && isTriggerTheSameTeamButMe
                    }

                    66 -> {
                        //自己获得负面效果时
                        triggeringSkill?.isGettingDebuff() == true && isTriggerTheOwner
                    }

                    67 -> {
                        //任意敌方获得负面效果时
                        triggeringSkill?.isGettingDebuff() == true && isTriggerTheEnemyTeam
                    }

                    68 -> {
                        //自己获得负面效果时
                        triggeringSkill?.isGettingDebuff() == true && isTriggerTheSameTeamButMe
                    }

                    69 -> {
                        // 自己获得任意控制效果时
                        isTriggerTheOwner && triggeringSkill?.isGettingDebuff() == true && triggeringSkill.buffInfo?.isControl() == true
                    }

                    70 -> {
                        // 任意敌方获得任意控制效果时
                        isTriggerTheEnemyTeam && triggeringSkill?.isGettingDebuff() == true && triggeringSkill.buffInfo?.isControl() == true
                    }

                    71 -> {
                        // 任意敌方获得任意控制效果时
                        isTriggerTheSameTeamButMe && triggeringSkill?.isGettingDebuff() == true && triggeringSkill.buffInfo?.isControl() == true
                    }

                    72 -> {
                        // 自己成功驱散任意敌方时
                        triggeringSkill?.isDispelEnemy() == true && isTriggerTheOwner
                    }

                    73 -> {
                        // 任意敌方成功驱散自己时
                        triggeringSkill?.isDispelEnemy() == true && isTriggerTheEnemyTeam
                    }

                    74 -> {
                        // 自己成功驱散自己时
                        triggeringSkill?.isDispelMyself() == true && isTriggerTheOwner
                    }

                    75 -> {
                        // 任意敌方成功驱散任意敌方时
                        triggeringSkill?.isDispelMyself() == true && isTriggerTheEnemyTeam
                    }

                    76 -> {
                        // 自己对自己造成伤害时
                        triggeringSkill?.isSelfHarm() == true && isTriggerTheOwner
                    }

                    77 -> {
                        // 自己对任意友方（不包括自己）造成伤害时
                        triggeringSkill?.isWounded() == true && isTriggerTheSameTeamButMe
                    }

                    78 -> {
                        // 每当自己召唤一个召唤物时
                        triggeringSkill?.isSummonMinion() == true && isTriggerTheOwner
                    }

                    79 -> {
                        // 每当任意敌方召唤一个召唤物时
                        triggeringSkill?.isSummonMinion() == true && isTriggerTheEnemyTeam
                    }

                    80 -> {
                        // 每当任意友方（不包括自己）召唤一个召唤物时
                        triggeringSkill?.isSummonMinion() == true && isTriggerTheSameTeamButMe
                    }

                    in 101..199 -> {
                        // 自己某个buff达到指定层数(buffId通过activeConditionNum表达，层数通过后续两位数字表达)
                        skillOwner.getShowBothBuff()
                            .find { it.id == activeConditionNum[index] }?.buffCurrentLayer?.takeIf {
                                it >= (abs(
                                    condition
                                ) - 100)
                            } != null
                    }

                    in 201..299 -> {
                        // 任意敌方的某个buff达到指定层数(buffId通过activeConditionNum表达，层数通过后续两位数字表达)
                        peer.getShowBothBuff()
                            .find { it.id == activeConditionNum[index] }?.buffCurrentLayer?.takeIf {
                                it >= (abs(
                                    condition
                                ) - 200)
                            } != null
                    }

                    in 30000..39999 -> {
                        // 自己释放指定某个技能达到xx次时（右侧字段填写大类技能ID）
                        val num = condition - 30000
                        triggeringSkill?.isCastSkill() == true && isTriggerTheOwner && triggeringSkill.mainId == activeConditionNum[index]
                                && field.getCastSkills(skillOwner)
                            .filter { it.mainId == activeConditionNum[index] }.size >= num
                    }

                    301 -> {
                        // 自己释放指定某个技能时（右侧字段填写大类技能ID）
                        triggeringSkill?.isCastSkill() == true && isTriggerTheOwner && triggeringSkill.mainId == activeConditionNum[index]
                    }

                    302 -> {
                        // 自己释放任意技能次数达N次时(可重复触发)
                        isTriggerTheOwner && triggeringSkill?.isCastSkill() == true && field.getCastSkills(
                            skillOwner
                        ).size % activeConditionNum[index] == 0
                    }

                    in 303..306 -> {
                        // 自己释放主动/触发/被动/普攻技能达N次时(可重复触发)
                        val triggerType = condition - 302
                        isTriggerTheOwner && triggeringSkill?.isCastSkill() == true && triggeringSkill.triggerType == triggerType && field.getCastSkills(
                            skillOwner
                        )
                            .filter { it.triggerType == triggerType }.size % activeConditionNum[index] == 0
                    }

                    in 311..320 -> {
                        // 自己释放的技能X系技能达N次时(可重复触发)
                        val type = condition - 310
                        isTriggerTheOwner && triggeringSkill?.isCastSkill() == true && triggeringSkill.elementType == type && field.getCastSkills(
                            skillOwner
                        ).filter { it.elementType == type }.size % activeConditionNum[index] == 0
                    }

                    351 -> {
                        // 任意敌方释放指定某个技能时（右侧字段填写大类技能ID）
                        isTriggerTheEnemyTeam && triggeringSkill?.mainId == activeConditionNum[index]
                    }

                    352 -> {
                        // 所有敌方总计释放任意技能次数达N次时(可重复触发)
                        isTriggerTheEnemyTeam && triggeringSkill?.isCastSkill() == true && field.getPeerTeamRoles(
                            skillOwner
                        ).sumOf {
                            field.getCastSkills(
                                it
                            ).size
                        } % activeConditionNum[index] == 0
                    }

                    in 353..356 -> {
                        // 所有敌方总计释放主动/触发/被动/普攻技能达N次时(可重复触发)
                        val triggerType = condition - 352
                        isTriggerTheEnemyTeam && triggeringSkill?.isCastSkill() == true && triggeringSkill.triggerType == triggerType && field.getPeerTeamRoles(
                            skillOwner
                        ).sumOf { role ->
                            field.getCastSkills(
                                role
                            ).filter { it.triggerType == triggerType }.size
                        } % activeConditionNum[index] == 0
                    }

                    in 361..370 -> {
                        // 所有敌方释放的技能X系技能达N次时(可重复触发)
                        val type = condition - 360
                        isTriggerTheEnemyTeam && triggeringSkill?.isCastSkill() == true && triggeringSkill.elementType == type && field.getPeerTeamRoles(
                            skillOwner
                        ).sumOf { role ->
                            field.getCastSkills(
                                role
                            ).filter { it.elementType == type }.size
                        } % activeConditionNum[index] == 0
                    }

                    402 -> {
                        // 自己释放任意技能次数达N次时
                        isTriggerTheOwner && triggeringSkill?.isCastSkill() == true && field.getCastSkills(
                            skillOwner
                        ).size >= activeConditionNum[index]
                    }

                    in 403..406 -> {
                        // 自己释放主动/触发/被动/普攻技能达N次时
                        val triggerType = condition - 402
                        isTriggerTheOwner && triggeringSkill?.isCastSkill() == true && triggeringSkill.triggerType == triggerType && field.getCastSkills(
                            skillOwner
                        )
                            .filter { it.triggerType == triggerType }.size >= activeConditionNum[index]
                    }

                    in 411..420 -> {
                        // 自己释放的技能X系技能达N次时
                        val type = condition - 410
                        isTriggerTheOwner && triggeringSkill?.isCastSkill() == true && triggeringSkill.elementType == type && field.getCastSkills(
                            skillOwner
                        ).filter { it.elementType == type }.size >= activeConditionNum[index]
                    }

                    451 -> {
                        // 任意敌方释放指定某个技能时（右侧字段填写大类技能ID）
                        isTriggerTheEnemyTeam && triggeringSkill?.mainId == activeConditionNum[index]
                    }

                    452 -> {
                        // 所有敌方总计释放任意技能次数达N次时
                        isTriggerTheEnemyTeam && triggeringSkill?.isCastSkill() == true && field.getPeerTeamRoles(
                            skillOwner
                        ).sumOf {
                            field.getCastSkills(
                                it
                            ).size
                        } >= activeConditionNum[index]
                    }

                    in 453..456 -> {
                        // 所有敌方总计释放主动/触发/被动/普攻技能达N次时
                        val triggerType = condition - 452
                        isTriggerTheEnemyTeam && triggeringSkill?.isCastSkill() == true && triggeringSkill.triggerType == triggerType && field.getPeerTeamRoles(
                            skillOwner
                        ).sumOf { role ->
                            field.getCastSkills(
                                role
                            ).filter { it.triggerType == triggerType }.size
                        } >= activeConditionNum[index]
                    }

                    in 461..470 -> {
                        // 所有敌方释放的技能X系技能达N次时
                        val type = condition - 460
                        isTriggerTheEnemyTeam && triggeringSkill?.isCastSkill() == true && triggeringSkill.elementType == type && field.getPeerTeamRoles(
                            skillOwner
                        ).sumOf { role ->
                            field.getCastSkills(
                                role
                            ).filter { it.elementType == type }.size
                        } >= activeConditionNum[index]
                    }

                    601 -> {
                        // 自己造成任意伤害达N次时(可重复触发)
                        val size = field.getDamageResults(
                            skillOwner
                        ).filter { it.damageValue.finalDamage > 0 }.size
                        triggeringSkill?.isCastDamage() == true && (triggeringSkill.damageResult?.damageValue?.finalDamage
                            ?: 0) > 0 && isTriggerTheOwner && size > 0 && size >= activeConditionNum[index]
                    }

                    in 611..620 -> {
                        // 自己造成1-10系伤害达N次时(可重复触发)
                        val type = condition - 610
                        val size = field.getDamageResults(
                            skillOwner
                        )
                            .filter { it.damageValue.finalDamage > 0 && it.type.value == type }.size
                        triggeringSkill?.isCastDamage() == true && triggeringSkill.damageResult?.type?.value == type && triggeringSkill.damageResult.damageValue.finalDamage > 0 && isTriggerTheOwner && size > 0 && size >= activeConditionNum[index]
                    }

                    651 -> {
                        // 自己受到任意伤害达N次时(可重复触发)
                        val size = field.getWoundedResults(
                            skillOwner
                        ).filter { it.damageValue.finalDamage > 0 }.size
                        triggeringSkill?.isWounded() == true && (triggeringSkill.damageResult?.damageValue?.finalDamage
                            ?: 0) > 0 && isTriggerTheOwner && size > 0 && size >= activeConditionNum[index]
                    }

                    in 661..670 -> {
                        // 自己受到1-10系伤害达N次时(可重复触发)
                        val type = condition - 660
                        val size = field.getWoundedResults(
                            skillOwner
                        )
                            .filter { it.damageValue.finalDamage > 0 && it.type.value == type }.size
                        triggeringSkill?.isWounded() == true && triggeringSkill.damageResult?.type?.value == type && triggeringSkill.damageResult.damageValue.finalDamage > 0 && isTriggerTheOwner && size > 0 && size >= activeConditionNum[index]
                    }

                    501 -> {
                        // 自己造成任意伤害达N次时
                        val size = field.getDamageResults(
                            skillOwner
                        ).filter { it.damageValue.finalDamage > 0 }.size
                        triggeringSkill?.isCastDamage() == true && (triggeringSkill.damageResult?.damageValue?.finalDamage
                            ?: 0) > 0 && isTriggerTheOwner && size > 0 && size % activeConditionNum[index] == 0
                    }

                    in 511..520 -> {
                        // 自己造成1-10系伤害达N次时
                        val type = condition - 510
                        val size = field.getDamageResults(
                            skillOwner
                        )
                            .filter { it.damageValue.finalDamage > 0 && it.type.value == type }.size
                        triggeringSkill?.isCastDamage() == true && triggeringSkill.damageResult?.type?.value == type && triggeringSkill.damageResult.damageValue.finalDamage > 0 && isTriggerTheOwner && size > 0 && size % activeConditionNum[index] == 0
                    }

                    551 -> {
                        // 自己受到任意伤害达N次时
                        val size = field.getWoundedResults(
                            skillOwner
                        ).filter { it.damageValue.finalDamage > 0 }.size
                        triggeringSkill?.isWounded() == true && (triggeringSkill.damageResult?.damageValue?.finalDamage
                            ?: 0) > 0 && isTriggerTheOwner && size > 0 && size % activeConditionNum[index] == 0
                    }

                    in 561..570 -> {
                        // 自己受到1-10系伤害达N次时
                        val type = condition - 560
                        val size = field.getWoundedResults(
                            skillOwner
                        )
                            .filter { it.damageValue.finalDamage > 0 && it.type.value == type }.size
                        triggeringSkill?.isWounded() == true && triggeringSkill.damageResult?.type?.value == type && triggeringSkill.damageResult.damageValue.finalDamage > 0 && isTriggerTheOwner && size > 0 && size % activeConditionNum[index] == 0
                    }

                    580 -> {
                        // 自己受到普攻伤害达N次时
                        val size = field.getWoundedResults(
                            skillOwner
                        ).filter { it.damageValue.finalDamage > 0 && it.damageSkill.isNormalAttackType() }.size
                        triggeringSkill?.isWounded() == true && field.getWoundedResults(
                            skillOwner
                        ).lastOrNull()?.damageSkill?.isNormalAttackType() == true && (triggeringSkill.damageResult?.damageValue?.finalDamage
                            ?: 0) > 0 && isTriggerTheOwner && size > 0 && size % activeConditionNum[index] == 0
                    }

                    in 701..710 -> {
                        // 若自己是种族1-10时
                        val raceType = condition - 700
                        skillOwner.getRace().raceType == raceType
                    }

                    in 711..720 -> {
                        // 若任意敌方是种族1-10时
                        val raceType = condition - 710
                        field.getPeerTeamRoles(skillOwner).any { role ->
                            !role.getAlly().isHero() && role.getRace().raceType == raceType
                        }
                    }

                    in 731..740 -> {
                        // 若任意友方是种族1时，不包括自己
                        val raceType = condition - 730
                        field.getMyTeamRoles(skillOwner)
                            .filterNot { it.playerId() == skillOwner.playerId() }.any { role ->
                                !role.getAlly().isHero() && role.getRace().raceType == raceType
                            }
                    }

                    in 741..760 -> {
                        // 若自己是阵营1-10时
                        val groupType = condition - 740
                        skillOwner.getRace().raceType2 == groupType
                    }

                    in 761..780 -> {
                        // 若任意敌方是阵营1-10时
                        val groupType = condition - 760
                        field.getPeerTeamRoles(skillOwner).any { role ->
                            !role.getAlly().isHero() && role.getRace().raceType2 == groupType
                        }
                    }

                    in 781..800 -> {
                        // 若任意友方是阵营1时，不包括自己
                        val groupType = condition - 780
                        field.getMyTeamRoles(skillOwner)
                            .filterNot { it.playerId() == skillOwner.playerId() }.any { role ->
                                !role.getAlly().isHero() && role.getRace().raceType2 == groupType
                            }
                    }

                    801 -> {
                        // 场上己方总人数（包括召唤物）小于等于N时（负数为大于）
                        if (activeConditionNum[index] > 0) {
                            field.getMyTeamRoles(skillOwner).size <= activeConditionNum[index]
                        } else {
                            field.getMyTeamRoles(skillOwner).size > -activeConditionNum[index]
                        }
                    }

                    802 -> {
                        // 场上敌方总人数（包括召唤物）小于等于N时（负数为大于）
                        if (activeConditionNum[index] > 0) {
                            field.getPeerTeamRoles(skillOwner).size <= activeConditionNum[index]
                        } else {
                            field.getPeerTeamRoles(skillOwner).size > -activeConditionNum[index]
                        }
                    }

                    803 -> {
                        // 场上总人数（包括召唤物）小于等于N时（负数为大于）
                        if (activeConditionNum[index] > 0) {
                            field.getAllRoles().size <= activeConditionNum[index]
                        } else {
                            field.getAllRoles().size > -activeConditionNum[index]
                        }
                    }

                    804 -> {
                        // 场上己方总人数（不包括召唤物）小于等于N时（负数为大于）
                        if (activeConditionNum[index] > 0) {
                            field.getMyTeamRoles(skillOwner)
                                .filter { !it.isMinion() }.size <= activeConditionNum[index]
                        } else {
                            field.getMyTeamRoles(skillOwner)
                                .filter { !it.isMinion() }.size > -activeConditionNum[index]
                        }
                    }

                    805 -> {
                        // 场上敌方总人数（不包括召唤物）小于等于N时（负数为大于）
                        if (activeConditionNum[index] > 0) {
                            field.getPeerTeamRoles(skillOwner)
                                .filter { !it.isMinion() }.size <= activeConditionNum[index]
                        } else {
                            field.getPeerTeamRoles(skillOwner)
                                .filter { !it.isMinion() }.size > -activeConditionNum[index]
                        }
                    }

                    806 -> {
                        // 场上总人数（不包括召唤物）小于等于N时（负数为大于）
                        if (activeConditionNum[index] > 0) {
                            field.getAllRoles()
                                .filter { !it.isMinion() }.size <= activeConditionNum[index]
                        } else {
                            field.getAllRoles()
                                .filter { !it.isMinion() }.size > -activeConditionNum[index]
                        }
                    }

                    807 -> {
                        // 场上己方总召唤物小于等于N时（负数为大于）
                        if (activeConditionNum[index] > 0) {
                            field.getMyTeamRoles(skillOwner)
                                .filter { it.isMinion() }.size <= activeConditionNum[index]
                        } else {
                            field.getMyTeamRoles(skillOwner)
                                .filter { it.isMinion() }.size > -activeConditionNum[index]
                        }
                    }

                    808 -> {
                        // 场上敌方总召唤物小于等于N时（负数为大于）
                        if (activeConditionNum[index] <= 0) {
                            field.getPeerTeamRoles(skillOwner)
                                .filter { it.isMinion() }.size <= activeConditionNum[index]
                        } else {
                            field.getPeerTeamRoles(skillOwner)
                                .filter { it.isMinion() }.size > -activeConditionNum[index]
                        }
                    }

                    809 -> {
                        // 场上总召唤物小于等于N时（负数为大于）
                        if (activeConditionNum[index] > 0) {
                            field.getAllRoles()
                                .filter { it.isMinion() }.size <= activeConditionNum[index]
                        } else {
                            field.getAllRoles()
                                .filter { it.isMinion() }.size > -activeConditionNum[index]
                        }
                    }

                    810 -> {
                        // 自己有召唤物时（负数为自己没有召唤物时），现在要判断这个召唤物是自己的
                        if (activeConditionNum[index] > 0) {
                            field.getMyMinion(skillOwner) != null
                        } else {
                            field.getMyMinion(skillOwner) == null
                        }
                    }

                    in 900..910 -> {
                        val fieldId = abs(condition) - 900
                        field.getEnvironment().id == fieldId
                    }

                    in 1001..1012 -> {
                        val propIndex = abs(condition) - 1000
                        if (activeConditionNum[index] > 0) {
                            skillOwner.getCurrentProperty()
                                .getPropertyByTarget(propIndex) > peer.getCurrentProperty()
                                .getPropertyByTarget(propIndex)
                        } else {
                            skillOwner.getCurrentProperty()
                                .getPropertyByTarget(propIndex) <= peer.getCurrentProperty()
                                .getPropertyByTarget(propIndex)
                        }
                    }

                    in 1101..1112 -> {
                        val propIndex = abs(condition) - 1100
                        if (activeConditionNum[index] > 0) {
                            skillOwner.getCurrentProperty()
                                .getPropertyByTarget(propIndex) > peer.getCurrentProperty()
                                .getPropertyByTarget(propIndex)
                        } else {
                            skillOwner.getCurrentProperty()
                                .getPropertyByTarget(propIndex) <= peer.getCurrentProperty()
                                .getPropertyByTarget(propIndex)
                        }
                    }

                    in 1401..1415 -> {
                        // 判断自己的属性1，如果大于等于要求值则满足条件，要求值配置在参数字段，填负数表示小于
                        val propIndex = abs(condition) - 1400
                        if (activeConditionNum[index] > 0) {
                            skillOwner.getCurrentProperty()
                                .getPropertyByTarget(propIndex) >= activeConditionNum[index]
                        } else {
                            skillOwner.getCurrentProperty()
                                .getPropertyByTarget(propIndex) <= -activeConditionNum[index]
                        }
                    }

                    in 1421..1435 -> {
                        // 判断敌方的属性1，如果大于等于要求值则满足条件，要求值配置在参数字段，填负数表示小于
                        val propIndex = abs(condition) - 1420
                        if (activeConditionNum[index] > 0) {
                            peer.getCurrentProperty()
                                .getPropertyByTarget(propIndex) >= activeConditionNum[index]
                        } else {
                            peer.getCurrentProperty()
                                .getPropertyByTarget(propIndex) <= -activeConditionNum[index]
                        }
                    }

                    1601 -> {
                        // 任意友方（包括自己）造成任意伤害达N次时
                        field.getMyTeamRoles(skillOwner).sumOf {
                            field.getDamageResults(
                                it
                            ).filter { it.damageValue.finalDamage > 0 }.size
                        } >= activeConditionNum[index]
                    }

                    in 1602..1611 -> {
                        // 任意友方（包括自己）造成X类伤害达N次时
                        val damageType = condition - 1601
                        field.getMyTeamRoles(skillOwner).sumOf {
                            field.getDamageResults(
                                it
                            ).filter { it.damageValue.finalDamage > 0 && it.type.value == damageType }.size
                        } >= activeConditionNum[index]
                    }

                    1612 -> {
                        // 任意友方（包括自己）受到任意伤害达N次时
                        field.getMyTeamRoles(skillOwner).sumOf {
                            field.getWoundedResults(
                                it
                            ).filter { it.damageValue.finalDamage > 0 }.size
                        } >= activeConditionNum[index]
                    }

                    in 1613..1622 -> {
                        // 任意友方（包括自己）受到X类伤害达N次时
                        val damageType = condition - 1612
                        field.getMyTeamRoles(skillOwner).sumOf {
                            field.getWoundedResults(
                                it
                            ).filter { it.damageValue.finalDamage > 0 && it.type.value == damageType }.size
                        } >= activeConditionNum[index]
                    }

                    1623 -> {
                        // 任意友方（不包括自己）造成任意伤害达N次时
                        field.getMyTeamRoles(skillOwner)
                            .filterNot { it.playerId() == skillOwner.playerId() }.sumOf {
                                field.getDamageResults(
                                    it
                                ).filter { it.damageValue.finalDamage > 0 }.size
                            } >= activeConditionNum[index]
                    }

                    in 1624..1633 -> {
                        // 任意友方（不包括自己）造成X类伤害达N次时
                        val damageType = condition - 1623
                        field.getMyTeamRoles(skillOwner)
                            .filterNot { it.playerId() == skillOwner.playerId() }.sumOf {
                                field.getDamageResults(
                                    it
                                ).filter { it.damageValue.finalDamage > 0 && it.type.value == damageType }.size
                            } >= activeConditionNum[index]
                    }

                    1634 -> {
                        // 任意友方（不包括自己）受到任意伤害达N次时
                        field.getMyTeamRoles(skillOwner)
                            .filterNot { it.playerId() == skillOwner.playerId() }.sumOf {
                                field.getWoundedResults(
                                    it
                                ).filter { it.damageValue.finalDamage > 0 }.size
                            } >= activeConditionNum[index]
                    }

                    in 1635..1644 -> {
                        // 任意友方（不包括自己）受到X类伤害达N次时
                        val damageType = condition - 1634
                        field.getMyTeamRoles(skillOwner)
                            .filterNot { it.playerId() == skillOwner.playerId() }.sumOf {
                                field.getWoundedResults(
                                    it
                                ).filter { it.damageValue.finalDamage > 0 && it.type.value == damageType }.size
                            } >= activeConditionNum[index]
                    }

                    1901 -> {
                        // 敌方造成任意伤害达N次时
                        field.getPeerTeamRoles(skillOwner).sumOf {
                            field.getDamageResults(
                                it
                            ).filter { it.damageValue.finalDamage > 0 }.size
                        } >= activeConditionNum[index]
                    }

                    in 1902..1911 -> {
                        // 敌方造成1-10类伤害达N次时
                        val damageType = condition - 1901
                        field.getPeerTeamRoles(skillOwner).sumOf {
                            field.getDamageResults(
                                it
                            ).filter { it.damageValue.finalDamage > 0 && it.type.value == damageType }.size
                        } >= activeConditionNum[index]
                    }

                    1912 -> {
                        // 敌方受到任意伤害达N次时
                        field.getPeerTeamRoles(skillOwner).sumOf {
                            field.getWoundedResults(
                                it
                            ).filter { it.damageValue.finalDamage > 0 }.size
                        } >= activeConditionNum[index]
                    }

                    in 1913..1919 -> {
                        // 敌方受到1-7类伤害达N次时
                        val damageType = condition - 1912
                        field.getPeerTeamRoles(skillOwner).sumOf {
                            field.getWoundedResults(
                                it
                            ).filter { it.damageValue.finalDamage > 0 && it.type.value == damageType }.size
                        } >= activeConditionNum[index]
                    }

                    else -> true
                }
                // 支持配置condition为负数，负数则表示取反
                conditionTrigger.add(if (condition >= 0) conditionResult else !conditionResult)
            }
            // 这里是支持条件逻辑，与或者非
            return (if (conditionLogic == 1) conditionTrigger.all { it }
            else conditionTrigger.any { it }).let {
                if (it) {
                    (if (triggerType == TriggerType.ACTIVE.value) {
                        if (!skillOwner.checkStatusCanActiveSkill(true)) false
                        else it
                    } else if (triggerType == TriggerType.TRIGGER.value) {
                        if (!skillOwner.checkStatusCanTriggerSkill(true)) false
                        else it
                    } else if (triggerType == TriggerType.NORMAL_ATTACK.value) {
                        if (!skillOwner.checkStatusCanNormalAttack(true)) false
                        else it
                    } else it).let {
                        // xx系是独立于主动被动，所以要再筛选一层
                        if (it && !skillOwner.checkCanElementSkill(elementType, true)) false
                        else it
                    }
                } else it
            }
        }
    }
}