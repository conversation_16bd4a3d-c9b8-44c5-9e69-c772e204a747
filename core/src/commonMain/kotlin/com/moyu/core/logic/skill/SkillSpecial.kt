package com.moyu.core.logic.skill

import com.moyu.core.model.skill.Skill
import com.moyu.core.util.RANDOM

fun Skill.special(): List<Int> {
    return when (special) {
        3 -> {
            // 随机选择一个子效果触发
            val index = RANDOM.nextInt(effectType.size)
            listOf(index)
        }
        4 -> {
            // 固定第一个子效果，其余随机一个
            mutableListOf(0).apply {
                add(1 + RANDOM.nextInt(effectType.size - 1))
            }
        }
        5 -> {
            // 所有子效果随机打乱顺序
            0.rangeTo(effectType.size).toList().shuffled(RANDOM)
        }
        6 -> {
            // 子效果每次都随机选一个，可重复发生
            mutableListOf<Int>().apply {
                repeat(effectType.size) {
                    add(it)
                }
            }
        }
        7 -> {
            // 随机选择两个个子效果触发（可重复）
            val index1 = RANDOM.nextInt(effectType.size)
            val index2 = RANDOM.nextInt(effectType.size)
            listOf(index1, index2)
        }
        8 -> {
            // 随机选择两个个子效果触发（不可重复）
            (effectType.indices).toList().shuffled(RANDOM).take(2)
        }
        9 -> {
            // 触发随机一个combinedBuff
            combinedBuffId.toList().mapIndexed { index, i ->
                Pair(index, i)
            }.groupBy { it.second }.entries.shuffled(RANDOM).first().value.map { it.first }
        }
        10 -> {
            // 触发随机2个combinedBuff
            combinedBuffId.toList().mapIndexed { index, i ->
                Pair(index, i)
            }.groupBy { it.second }.entries.shuffled(RANDOM).take(2).flatMap { it.value }.map { it.first }
        }
        else -> {
            // 正常，触发所有子效果
            effectType.indices.toList()
        }
    }
}