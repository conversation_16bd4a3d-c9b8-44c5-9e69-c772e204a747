#!/bin/bash

set -e

# 配置
SCHEME="iosApp"
CONFIGURATION="Release"
WORKSPACE="iosApp.xcworkspace"
ARCHIVE_PATH="build/iosApp.xcarchive"
IPA_PATH="build/iosApp.ipa"
EXPORT_OPTIONS_PLIST="ExportOptions.plist"  # 配置是否签名 / 上传等

echo "🔨 Cleaning..."
xcodebuild clean -workspace "$WORKSPACE" -scheme "$SCHEME"

echo "📦 Archiving..."
xcodebuild archive \
  -workspace "$WORKSPACE" \
  -scheme "$SCHEME" \
  -configuration "$CONFIGURATION" \
  -archivePath "$ARCHIVE_PATH" \
  -sdk iphoneos \
  -allowProvisioningUpdates \
  -quiet

echo "📤 Exporting IPA..."
xcodebuild -exportArchive \
  -archivePath "$ARCHIVE_PATH" \
  -exportPath "$(dirname "$IPA_PATH")" \
  -exportOptionsPlist "$EXPORT_OPTIONS_PLIST" \
  -quiet

echo "✅ IPA created at $IPA_PATH"