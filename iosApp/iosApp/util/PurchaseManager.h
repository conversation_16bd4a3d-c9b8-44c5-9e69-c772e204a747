//
//  PurchaseManager.h
//  iosApp
//
//  Created by quding on 2025/1/6.
//  Copyright © 2025 hour. All rights reserved.
//

#import <Foundation/Foundation.h>

#import <StoreKit/StoreKit.h>

NS_ASSUME_NONNULL_BEGIN

typedef NS_ENUM(NSInteger, IAPPurchaseStatus) {
    IAPPurchaseFailed,      // 购买未成功
    IAPPurchaseSucceeded,   // 购买成功
    IAPRestoredFailed,      // 恢复产品不成功
    IAPRestoredSucceeded,   // 恢复产品成功
    IAPDownloadStarted,     // 下载托管内容已经开始
    IAPDownloadInProgress,  // 当前正在下载托管内容
    IAPDownloadFailed,      // 下载托管内容失败
    IAPDownloadSucceeded    // 托管内容已成功下载
};


@interface PurchaseManager : NSObject<SKPaymentTransactionObserver,SKProductsRequestDelegate>

/// 状态
@property (nonatomic, assign, readonly) IAPPurchaseStatus status;
/// 购买结果票据
@property (nonatomic, strong, readonly) NSString *receiptData;
/// 订单地址
@property (nonatomic, strong, readonly) NSString *transactionId;
/// 失败原因
@property (nonatomic, strong, readonly) NSString *errMessage;
/// 内购中的产品ID
@property (nonatomic, strong, readonly) NSString *productId;
/// 业务的订单id
@property (nonatomic, strong, readonly) NSString *payOrderNo;


@property (nonatomic, copy) dispatch_block_t paymentTransactionDidFinish;
@property (nonatomic, copy) dispatch_block_t didReceiveProductFailed;

/**
 * 发起内购
 * 消耗性购买，会先请求订单payOrderNo，验证接口需要
 * 订阅购买，不需要订单payOrderNo
 */
- (void)launchInAppPurchase:(NSString *)productId payOrderNo:(NSString * __nullable)payOrderNo;

@end

NS_ASSUME_NONNULL_END
