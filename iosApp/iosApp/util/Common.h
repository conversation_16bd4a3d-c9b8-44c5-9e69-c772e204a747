//
//  Common.h
//  iosApp
//
//  Created by quding on 2025/1/14.
//  Copyright © 2025 hour. All rights reserved.
//

#import <Foundation/Foundation.h>

#define APPBuildNo                  [[NSBundle mainBundle] infoDictionary][@"CFBundleVersion"]
#define APPVersionCode              [[NSBundle mainBundle] infoDictionary][@"CFBundleShortVersionString"]


NS_ASSUME_NONNULL_BEGIN

@interface Common : NSObject

+ (NSString *)transformVersionCode;

@end


@interface KDSoundPlayer : NSObject

+ (instancetype)sharedInstance;

- (void)playSoundNamed:(NSString *)name;
- (void)disposeAllSounds;

@end

NS_ASSUME_NONNULL_END
