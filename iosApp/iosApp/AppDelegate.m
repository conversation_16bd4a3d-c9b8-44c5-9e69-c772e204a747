//
//  AppDelegate.m
//
//  Created by quding
//

#import "AppDelegate.h"
#import "TimeUtil.h"
#import "iosApp-Swift.h"
#import <SSZipArchive.h>
#import "MBProgressHUD.h"
@import Firebase;
@import shared;
#import <AppTrackingTransparency/AppTrackingTransparency.h>
#import <AFNetworking/AFNetworking.h>
#import "PurchaseManager.h"
#import "UIDevice+VGAddition.h"
#import "Common.h"
@import GoogleMobileAds;
@import UIKit;
//#import <FBSDKCoreKit/FBSDKCoreKit.h>
@import AdjustSdk;

@interface AppDelegate () <GADFullScreenContentDelegate>

@property (nonatomic, strong) PurchaseManager *purchaseManager;

@property(nonatomic, strong) GADRewardedAd *rewardedAd;

@property (nonatomic, copy) SharedKotlinUnit* (^adShowCallback)(SharedBoolean * _Nonnull);

@property (nonatomic, copy) NSString *productId;

@property(nonatomic, strong) MBProgressHUD *purchasingHud;

@end

@implementation AppDelegate

// 是否是将要发布的正式环境
// 测试阶段会屏蔽 firebase sdk ，adjust 设置为沙盒环境
- (BOOL)isReleaseEnv {
//    return YES;
    return NO;
}

- (BOOL)application:(UIApplication *)application didFinishLaunchingWithOptions:(NSDictionary *)launchOptions {
    
#ifdef DEBUG
//    [SharedMain_iosKt IOSKtInit];
#else
    [SharedMain_iosKt IOSKtInit];
#endif
    
    if ([self isReleaseEnv]) {
        [FIRApp configure];
    }
    
    // init facebook sdk
//    [[FBSDKApplicationDelegate sharedInstance] application:application
//                             didFinishLaunchingWithOptions:launchOptions];
//    [FBSDKAppEvents.shared activateApp];

    NSString *yourAppToken = @"vhonijnpozr4";
    NSString *environment = ADJEnvironmentSandbox;
    if ([self isReleaseEnv]) {
        environment =  ADJEnvironmentProduction;
    }
    ADJConfig *adjustConfig = [[ADJConfig alloc] initWithAppToken:yourAppToken environment:environment];
    [adjustConfig setLogLevel:ADJLogLevelVerbose];
    [Adjust initSdk:adjustConfig];
    
    // timestamp
    [SharedMain_iosKt setSystemElapsedBootTimeMillisBlock:^SharedLong * _Nonnull() {
        // 获取设备的启动时间
        NSTimeInterval us_uptime = [[NSProcessInfo processInfo] systemUptime] * 1000;
//        NSLog(@"=-== us_uptime: %@", @(us_uptime));

        return [[SharedLong alloc] initWithLongLong:us_uptime];
    }];
    
    {   // 多语言
        // 当前手机使用的语言数组
        NSArray* languageArray = [NSLocale preferredLanguages];
//        NSLog(@"=-== languageArray: %@", languageArray);
        
        NSString* language = [languageArray objectAtIndex:0];
        [SharedMain_iosKt setIosPhoneLanguage:language];
        
        [SharedMain_iosKt setIosSetAppLanguage:^(NSString * language) {
            [NSUserDefaults.standardUserDefaults setObject:@[language] forKey:@"AppleLanguages"];
            [NSUserDefaults.standardUserDefaults synchronize];
        }];
    }
    
    // app version
    [SharedMain_iosKt setIosIsReleasePackage:[self isReleaseEnv]];
    [SharedMain_iosKt setIosVersionName:APPVersionCode];
    [SharedMain_iosKt setIosVersionCode:[Common transformVersionCode]];
    
    // encrypt
    [SharedMain_iosKt setIosEncryptBlock:^NSString * _Nonnull(NSString * _Nonnull content, NSString * _Nonnull password) {
        if (content.length == 0) {
            return @"";
        }
        
        NSString *result = [CryptoUtil p_encryptWithContent:content password:password];
        return result;
    }];
    [SharedMain_iosKt setIosDecryptBlock:^NSString * _Nonnull(NSString * _Nonnull content, NSString * _Nonnull password) {
        if (content.length == 0) {
            return @"";
        }
        if (password.length == 0) {
            return @"";
        }
        
        NSString *result = [CryptoUtil p_decryptWithContent:content password:password];
        return result;
    }];
    
    // 压缩 & 解压缩
    [SharedMain_iosKt setIosZipBlock:^(NSString * zipFilePath, NSString * compressFilePath, NSString * password) {
        [SSZipArchive createZipFileAtPath:zipFilePath withFilesAtPaths:@[compressFilePath] withPassword:password];
    }];
    
    [SharedMain_iosKt setIosUnzipBlock:^void (NSString * _Nonnull zipFilePath, NSString * _Nonnull destination, NSString * _Nonnull password) {
        [SSZipArchive unzipFileAtPath:zipFilePath toDestination:destination overwrite:YES password:password progressHandler:^(NSString * _Nonnull entry, unz_file_info zipInfo, long entryNumber, long total) {
            NSLog(@"");
        }  completionHandler:^(NSString * _Nonnull path, BOOL succeeded, NSError * _Nullable error) {
            NSLog(@"=-== %@", error);
        }];
    }];
    
    // login
    SharedMain_iosKt.iosUserId = [SignInApple.shared getUserId];
    SharedMain_iosKt.iosUserName = [SignInApple.shared getUserName];
    [SharedMain_iosKt setIosStartLoginAction:^(SharedKotlinUnit *(^ callback)(void)) {
        [SignInApple.shared handleAuthorizationAppleIDWithCallback:^() {
            SharedMain_iosKt.iosUserId = [SignInApple.shared getUserId];
            SharedMain_iosKt.iosUserName = [SignInApple.shared getUserName];
            callback();
        }];
    }];
    
    // ui
    SharedMain_iosKt.iosScreenDensity = UIScreen.mainScreen.scale;
    SharedMain_iosKt.iosScreenHeightInPixel = UIScreen.mainScreen.nativeBounds.size.height;
    SharedMain_iosKt.iosScreenWidthInDp = UIScreen.mainScreen.bounds.size.width;
    SharedMain_iosKt.iosScreenHeightInDp = UIScreen.mainScreen.bounds.size.height;
    SharedMain_iosKt.iosStatusBarHeightInDp = [UIDevice vg_statusBarHeight];
    SharedMain_iosKt.iosBottomHeightInDp = [UIDevice vg_safeDistanceBottom];

    {   // network
        [[AFNetworkReachabilityManager sharedManager] setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
            if (status == AFNetworkReachabilityStatusReachableViaWiFi
                || status == AFNetworkReachabilityStatusReachableViaWWAN) {
                SharedMain_iosKt.iosHasNetworkPermission = YES;
            } else {
                SharedMain_iosKt.iosHasNetworkPermission = NO;
            }
        }];
        [[AFNetworkReachabilityManager sharedManager] startMonitoring];
        
        // network change callback
        [SharedMain_iosKt setIosCheckNetworkPermission:^(SharedKotlinUnit * _Nonnull (^ callback)(void)) {
            [[AFNetworkReachabilityManager sharedManager] setReachabilityStatusChangeBlock:^(AFNetworkReachabilityStatus status) {
                if (status == AFNetworkReachabilityStatusReachableViaWiFi
                    || status == AFNetworkReachabilityStatusReachableViaWWAN) {
                    SharedMain_iosKt.iosHasNetworkPermission = YES;
                } else {
                    SharedMain_iosKt.iosHasNetworkPermission = NO;
                }

                callback();
            }];
        }];
    }
    
    [SharedMain_iosKt setIosPlaySound:^(NSString * name) {
        [KDSoundPlayer.sharedInstance playSoundNamed:name];
    }];
    
    // in app purchase
    [SharedMain_iosKt setIosPurchaseProduct:^(NSString *productId, NSString *sellId, SharedDouble* dollarPrice, SharedKotlinUnit * _Nonnull (^callback)(void)) {
        MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.window.rootViewController.view animated:YES];
        hud.mode = MBProgressHUDModeIndeterminate;
        hud.label.text = @"Loading";
        self.purchasingHud = hud;
        
//        dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(3 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
//            callback();
//        });
    
        [self.purchaseManager setPaymentTransactionDidFinish:^{
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                if (self.purchaseManager.errMessage == nil && self.purchaseManager.status == IAPPurchaseSucceeded) {
                    if ([productId isEqualToString:self.purchaseManager.payOrderNo]) {
                        callback();
                        
                        ADJEvent *event = [[ADJEvent alloc] initWithEventToken:@"e40xeo"];
                        [event setRevenue:dollarPrice.doubleValue currency:@"USD"];
                        if (self.purchaseManager.transactionId.length > 0) {
                            [event setDeduplicationId:self.purchaseManager.transactionId];
                        }
                        [Adjust trackEvent:event];
                    }
                }
                
//                MBProgressHUD *hud = [MBProgressHUD showHUDAddedTo:self.window.rootViewController.view animated:YES];
//                hud.mode = MBProgressHUDModeIndeterminate;
//                hud.label.text = [NSString stringWithFormat:@"订单id: %@", self.purchaseManager.transactionId];
                dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                    if (self.purchasingHud != nil) {
                        [self.purchasingHud hideAnimated:YES];
                    }
                });
            });
        }];
        
        [self.purchaseManager setDidReceiveProductFailed:^{
            dispatch_async(dispatch_get_main_queue(), ^{
                [self.purchasingHud hideAnimated:YES];
            });
        }];
        
        self.productId = productId;
        [self.purchaseManager launchInAppPurchase:sellId payOrderNo:productId];
    }];

    // appflyer
    [self configAppsFlyer];
    
    // ad
    [self configAppAd];
    
#ifdef DEBUG
//    NSLog(@"=-== iosStatusBarHeightInDp %@", @(SharedMain_iosKt.iosStatusBarHeightInDp));
//    NSLog(@"=-== iosBottomHeightInDp %@", @(SharedMain_iosKt.iosBottomHeightInDp));
//    NSLog(@"=-== width %@", @(SharedMain_iosKt.iosScreenWidthInDp));
//    NSLog(@"=-== height %@", @(SharedMain_iosKt.iosScreenHeightInDp));
#endif
    
    UIViewController *controller = [SharedMain_iosKt ComposeEntryPoint];
    self.window.rootViewController = controller;
    
    return YES;
}

- (void)applicationWillResignActive:(UIApplication *)application {
    // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
    // Use this method to pause ongoing tasks, disable timers, and invalidate graphics rendering callbacks. Games should use this method to pause the game.
}

- (void)applicationDidEnterBackground:(UIApplication *)application {
    // Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
    // If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
}


- (void)applicationWillEnterForeground:(UIApplication *)application {
    // Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
}


- (void)applicationDidBecomeActive:(UIApplication *)application {
    [ATTrackingManager requestTrackingAuthorizationWithCompletionHandler:^(ATTrackingManagerAuthorizationStatus status) {
//      NSLog(@"Status: %lu", (unsigned long)status);
    }];
}

- (void)applicationWillTerminate:(UIApplication *)application {
    // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
}

- (PurchaseManager *)purchaseManager {
    if (_purchaseManager == nil) {
        _purchaseManager = [[PurchaseManager alloc] init];
    }
    
    return _purchaseManager;
}

#pragma mark - tools
- (void)configAppsFlyer {
    
    // event callback
    [SharedMain_iosKt setIosAppFlyerEvent:^(NSString *eventName, NSDictionary<NSString *,id> *params) {
        NSLog(@"=-=== eventName %@ params %@", eventName, params);
//        [[AppsFlyerLib shared] logEvent:eventName withValues: params];
        
        if ([eventName isEqualToString:@"af_login"]) {
            /*
              * Send af_login event.
              * Trigger: User successfully logs in
              */
//            @{
//            @"af_new_user": @"", //
//            AFEventParamNewVersion: @"", //
//            AFEventParamCustomerUserId: @"", //
//          }
//            [[AppsFlyerLib shared] logEvent:AFEventLogin withValues: params];
        } else if ([eventName isEqualToString:@"af_ad_view"]) {
            /*
              * Send af_ad_view event.
              * Trigger:
              */
//            @{
//            @"af_new_user": @"", //
//            @"af_ad_id": @"", //
//          }
//            [[AppsFlyerLib shared] logEvent:AFEventAdView withValues: params];
        } else if ([eventName isEqualToString:@"af_purchase"]) {
            /*
              * Send af_purchase event.
              * Trigger: User lands on the thank you page after a successful purchase
              */
//            @{
//            AFEventParamRevenue: @"", // Estimated revenue from the purchase. The revenue value should not contain comma separators, currency, special characters, or text.
//            AFEventParamCurrency: @"", // Currency code
//            AFEventParamQuantity: @"", // Number of items purchased
//            AFEventParamContentId: @"", // Item ID
//            AFEventParamOrderId: @"", // Order ID
//            AFEventParamReceiptId: @"", // Receipt ID
//            AFEventParamNewVersion: @"", //
//            @"af_ad_id": @"", //
//            @"af_new_user": @"", //
//            AFEventParamCustomerUserId: @"", //
//          }
//            [[AppsFlyerLib shared] logEvent:AFEventPurchase withValues: params];
        }
    }];
}

- (void)configAppAd {
    [self loadRewardedAd]; // load ad when app start
    
    [SharedMain_iosKt setIosAdLoadBlock:^(SharedKotlinUnit* (^callback)(SharedBoolean *)) {
        GADRequest *request = [GADRequest request];
        [GADRewardedAd
            loadWithAdUnitID:@"ca-app-pub-5058022002121914/5411784403"
                      request:request
         completionHandler:^(GADRewardedAd *ad, NSError *error) {
            if (error) {
                NSLog(@"=-== Rewarded ad failed to load with error: %@", [error localizedDescription]);
                callback([[SharedBoolean alloc] initWithBool:false]);
                return;
            }
            self.rewardedAd = ad;
            callback([[SharedBoolean alloc] initWithBool:true]);
            NSLog(@"=-== Rewarded ad loaded.");
            
            self.rewardedAd.paidEventHandler = ^void(GADAdValue *_Nonnull value) {
                // for more information, please check AdMob official docs at:
                // https://developers.google.com/admob/ios/impression-level-ad-revenue
                GADAdNetworkResponseInfo *loadedAdNetworkResponseInfo = self.rewardedAd.responseInfo.loadedAdNetworkResponseInfo;
                
                // send ad revenue info to Adjust
                ADJAdRevenue *adRevenue = [[ADJAdRevenue alloc] initWithSource:@"admob_sdk"];
                
                [adRevenue setRevenue:value.value.doubleValue currency:value.currencyCode];
                [adRevenue setAdRevenueNetwork:loadedAdNetworkResponseInfo.adSourceName];
                [Adjust trackAdRevenue:adRevenue];
            };
        }];
    }];
    
    [SharedMain_iosKt setIosAdShowBlock:^(SharedKotlinUnit* (^callback)(SharedBoolean * _Nonnull)) {
        if (self.rewardedAd) {
            // The UIViewController parameter is nullable.
            self.rewardedAd.fullScreenContentDelegate = self;
            [self.rewardedAd presentFromRootViewController:nil
                                  userDidEarnRewardHandler:^{
                self.adShowCallback = callback;
            }];
        } else {
            NSLog(@"=-== Ad wasn't ready");
            dispatch_after(dispatch_time(DISPATCH_TIME_NOW, (int64_t)(1 * NSEC_PER_SEC)), dispatch_get_main_queue(), ^{
                callback([[SharedBoolean alloc] initWithBool:false]);
            });
        }
    }];
}

- (void)loadRewardedAd {
    GADRequest *request = [GADRequest request];
    [GADRewardedAd
        loadWithAdUnitID:@"ca-app-pub-5058022002121914/5411784403"
                  request:request
     completionHandler:^(GADRewardedAd *ad, NSError *error) {
        if (error) {
            NSLog(@"=-== Rewarded ad failed to load with error: %@", [error localizedDescription]);
            return;
        }
        self.rewardedAd = ad;
        NSLog(@"=-== Rewarded ad loaded.");
        
        self.rewardedAd.paidEventHandler = ^void(GADAdValue *_Nonnull value) {
            // for more information, please check AdMob official docs at:
            // https://developers.google.com/admob/ios/impression-level-ad-revenue
            GADAdNetworkResponseInfo *loadedAdNetworkResponseInfo = self.rewardedAd.responseInfo.loadedAdNetworkResponseInfo;

            // send ad revenue info to Adjust
            ADJAdRevenue *adRevenue = [[ADJAdRevenue alloc]
                              initWithSource:@"admob_sdk"];
            
            [adRevenue setRevenue:value.value.doubleValue currency:value.currencyCode];
            [adRevenue setAdRevenueNetwork:loadedAdNetworkResponseInfo.adSourceName];
            [Adjust trackAdRevenue:adRevenue];
        };
    }];
}

#pragma mark  - GADFullScreenContentDelegate

- (void)adDidDismissFullScreenContent:(nonnull id<GADFullScreenPresentingAd>)ad {
    if (self.adShowCallback) {
        self.adShowCallback([[SharedBoolean alloc] initWithBool:true]);
    }
}

@end
