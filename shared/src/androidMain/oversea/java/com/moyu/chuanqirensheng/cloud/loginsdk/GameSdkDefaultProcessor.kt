package com.moyu.chuanqirensheng.cloud.loginsdk

import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.moyu.chuanqirensheng.api.RetrofitModel
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.guide.GuideManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.sub.bill.RC_SIGN_IN
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEW_USER
import com.moyu.chuanqirensheng.sub.datastore.KEY_OBJECT_ID
import com.moyu.chuanqirensheng.sub.datastore.getStringFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.datastore.setStringValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.sub.saver.CloudSaverManager
import com.moyu.chuanqirensheng.util.killSelf
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext


class GameSdkDefaultProcessor : GameSdkProcessor {
    private val antiAddictionContent = mutableStateOf("")
    private val loginStatus = mutableStateOf(false)
    private val avatar = mutableStateOf<String?>(null)
    private val userName = mutableStateOf<String?>(null)
    private val antiAddictionStatus = mutableStateOf(false)
    private val objectId = mutableStateOf<String?>(null)

    private var init = false
    private var mGoogleSignInClient: GoogleSignInClient? = null
    var mGoogleSignInAccount: GoogleSignInAccount? = null

    override fun initGameSdk() {
        antiAddictionStatus.value = false
    }

    // 初始化SDK，判断是否有登录态，没有则调用好游快爆登录，
    // 登录后校验防沉迷，若返回登录成功，则正常显示菜单，否则弹窗提示错误原因，并退出
    override fun initSDK() {
        if (init) return
        init = true

        val activity = GameApp.instance.activity
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestEmail()
            .build()
        mGoogleSignInClient = GoogleSignIn.getClient(activity, gso)
        login()
    }

    override fun login() {
        val activity = GameApp.instance.activity

        GoogleSignIn.getLastSignedInAccount(activity)?.let {account ->
            mGoogleSignInAccount = account
            dealAfterLogin(account.displayName?: "No Name", account.id!!, account.photoUrl.toString())
        } ?: kotlin.run {
            val signInIntent = mGoogleSignInClient!!.signInIntent
            activity.startActivityForResult(signInIntent, RC_SIGN_IN)
        }
    }

    override fun antiAddictPassed(): MutableState<Boolean> {
        return antiAddictionStatus
    }

    override fun hasLogin(): Boolean {
        return loginStatus.value
    }

    override fun getAvatarUrl(): String {
        return if (avatar.value.isNullOrEmpty() || avatar.value == "null") "skill_7019" else avatar.value!!
    }

    override fun getUserName(): String? {
        return userName.value
    }

    override fun getObjectId(): String? {
        return objectId.value
    }

    override fun getAntiAddictionContent(): String {
        return antiAddictionContent.value
    }

    override fun dealAfterLogin(name: String, id: String, avatarUrl: String) {
        loginStatus.value = true
        userName.value = name
        objectId.value = id
        avatar.value = avatarUrl
        checkAntiAddiction()
        AppWrapper.globalScope.launch {
            val oldAccount = getStringFlowByKey(KEY_OBJECT_ID)
            if (oldAccount.isEmpty()) {
                setStringValueByKey(KEY_OBJECT_ID, objectId.value ?: "")
            } else if (oldAccount != objectId.value) {
                "不支持账号切换，请卸载重装".toast()
                delay(2000)
                killSelf()
            }
        }

        tryLogin()
    }

    override fun isAgeUnder8(): Boolean {
        return false
    }

    override fun isAgeIn8To16(): Boolean {
        return false
    }

    override fun checkAntiAddiction() {
    }

    override fun quitGame(onExit: () -> Unit) {
        onExit()
    }


    fun tryLogin() {
        AppWrapper.globalScope.launch(Dispatchers.IO) {
            RetrofitModel.getLoginData()
            AppWrapper.globalScope.launch(Dispatchers.Main) {
                repo.doInitAfterLogin()
                uploadRoleInfo()
            }
            GuideManager.showFirstGuide()
            if (LoginManager.instance.newUser) { // 新用户
                // 看下是不是有存档
                CloudSaverManager.checkIfNewUserHaveCloudSave()
            }
            setBooleanValueByKey(KEY_NEW_USER, false)
        }
    }

    override fun uploadRoleInfo() {

    }
}
