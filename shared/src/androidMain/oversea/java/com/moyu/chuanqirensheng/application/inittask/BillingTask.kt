package com.moyu.chuanqirensheng.application.inittask

import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.bill.BillingManager
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.core.AppWrapper
import kotlinx.coroutines.launch

/**
 * 谷歌订单sdk初始化
 */
class BillingTask : JobContent<Context> {
    override fun execute(context: Context) {
        AppWrapper.globalScope.launch {
            BillingManager.initBillingClient()
        }
    }
}
