package com.moyu.chuanqirensheng.application.inittask

import com.appsflyer.AppsFlyerLib
import com.google.android.gms.ads.MobileAds
import com.google.firebase.analytics.FirebaseAnalytics
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.sub.ad.AdHolder
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.chuanqirensheng.sub.report.ReportManager.firebaseAnalytics
import com.moyu.core.appFlyerDevKey


/**
 * 穿山甲广告sdk初始化
 */
class TTRewardAdTask : JobContent<Context> {
    override fun execute(context: Context) {
        MobileAds.initialize(GameApp.instance) {
            AdHolder.adInit = true
        }

        AppsFlyerLib.getInstance().init(appFlyerDev<PERSON><PERSON>, null, GameApp.instance)
        AppsFlyerLib.getInstance().start(GameApp.instance)

        // 初始化 Firebase Analytics
        firebaseAnalytics = FirebaseAnalytics.getInstance(GameApp.instance)
    }
}