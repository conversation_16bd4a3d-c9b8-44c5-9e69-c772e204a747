package com.moyu.chuanqirensheng.sub.bill

import android.content.Intent
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.tasks.Task
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.util.triggerRebirth
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

const val RC_SIGN_IN = 8668

object GoogleActivityResultHandler: ActivityResultHandler {
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        // Result returned from launching the Intent from GoogleSignInClient.getSignInIntent(...);
        if (requestCode == RC_SIGN_IN) {
            // The Task returned from this call is always completed, no need to attach
            // a listener.
            val task: Task<GoogleSignInAccount> =
                GoogleSignIn.getSignedInAccountFromIntent(data)
            handleSignInResult(task)
        }
    }

    private fun handleSignInResult(completedTask: Task<GoogleSignInAccount>) {
        try {
            val account: GoogleSignInAccount = completedTask.getResult(ApiException::class.java)
            GameApp.instance.dealAfterLogin(
                account.displayName ?: "No Name",
                account.id!!,
                account.photoUrl.toString(),
            )
//            "${account.id} ${account.displayName} ${account.photoUrl}".toast()
        } catch (e: ApiException) {
            ("Google account login failed, restart game in 3 seconds: " + e.statusCode).toast()
            AppWrapper.globalScope.launch {
                delay(3000)
                triggerRebirth()
            }
        }
    }

}