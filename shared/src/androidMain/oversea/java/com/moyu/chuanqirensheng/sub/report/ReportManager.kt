package com.moyu.chuanqirensheng.sub.report

import android.os.Bundle
import androidx.compose.runtime.mutableStateOf
import com.appsflyer.AFInAppEventParameterName
import com.appsflyer.AFInAppEventType
import com.appsflyer.AppsFlyerLib
import com.google.firebase.analytics.FirebaseAnalytics
import com.moyu.chuanqirensheng.application.GameApp
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.repository.Repo
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_AD
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_PURCHASE1
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_PURCHASE2
import com.moyu.chuanqirensheng.sub.datastore.KEY_REPORT_AF_SECOND_LOGIN
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.datastore.setBooleanValueByKey
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.util.getVersionCode


object ReportManager: ReportInterface {
    // 初始化 Firebase Analytics
    lateinit var firebaseAnalytics: FirebaseAnalytics
    val firstAFAd = mutableStateOf(false)
    val firstAFPurchase = mutableStateOf(false)
    val secondAFPurchase = mutableStateOf(false)
    val secondAFLogin = mutableStateOf(false)

    override fun setup() {
        firstAFAd.value = getBooleanFlowByKey(KEY_REPORT_AF_AD)
        firstAFPurchase.value = getBooleanFlowByKey(KEY_REPORT_AF_PURCHASE1)
        secondAFPurchase.value = getBooleanFlowByKey(KEY_REPORT_AF_PURCHASE2)
        secondAFLogin.value = getBooleanFlowByKey(KEY_REPORT_AF_SECOND_LOGIN)
    }

    override fun onLogin() {
        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues[AF_NEW_USER] = if (LoginManager.instance.newUser) 1 else 0
        eventValues[AFInAppEventParameterName.NEW_VERSION] = getVersionCode()
        eventValues[AFInAppEventParameterName.CUSTOMER_USER_ID] = GameApp.instance.getObjectId()?:"none"
        if (!LoginManager.instance.newUser && !secondAFLogin.value) {
            secondAFLogin.value = true
            setBooleanValueByKey(KEY_REPORT_AF_SECOND_LOGIN, true)
            AppsFlyerLib.getInstance().logEvent(GameApp.instance, AFInAppEventType.LOGIN, eventValues)
        } else {
            if (LoginManager.instance.newUser) {
                AppsFlyerLib.getInstance()
                    .logEvent(GameApp.instance, AFInAppEventType.LOGIN, eventValues)
            }
        }


        // 定义一个购买事件
        val bundle = bundleWithCommonParam()
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.LOGIN, bundle)
    }

    override fun onAdCompletedAF(adId: String) {
        if (!firstAFAd.value) {
            firstAFAd.value = true
            setBooleanValueByKey(KEY_REPORT_AF_AD, true)
            val eventValues: MutableMap<String, Any> = HashMap()
            eventValues[AF_AD_ID] = adId
            eventValues[AF_NEW_USER] = if (LoginManager.instance.newUser) 1 else 0
            eventValues[AFInAppEventParameterName.NEW_VERSION] = getVersionCode()
            eventValues[AFInAppEventParameterName.CUSTOMER_USER_ID] = GameApp.instance.getObjectId()?:"none"
            AppsFlyerLib.getInstance().logEvent(GameApp.instance, AFInAppEventType.AD_VIEW, eventValues)
        }

        // 定义一个购买事件
        val bundle = bundleWithCommonParam()
        bundle.putString(AF_AD_ID, adId)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.AD_IMPRESSION, bundle)
    }

    override fun onPurchaseCompletedAF(purchaseId: String, amount: Double, number: Int) {
        val eventValues: MutableMap<String, Any> = HashMap()
        eventValues[AFInAppEventParameterName.REVENUE] = amount
        eventValues[AFInAppEventParameterName.CONTENT_ID] = purchaseId
        eventValues[AF_NEW_USER] = if (LoginManager.instance.newUser) 1 else 0
        eventValues[AFInAppEventParameterName.NEW_VERSION] = getVersionCode()
        eventValues[AFInAppEventParameterName.QUANTITY] = number
        eventValues[AFInAppEventParameterName.CUSTOMER_USER_ID] = GameApp.instance.getObjectId()?:"none"
        if (!firstAFPurchase.value) {
            firstAFPurchase.value = true
            setBooleanValueByKey(KEY_REPORT_AF_PURCHASE1, true)
            eventValues[AF_SECOND_PURCHASE] = 0
            AppsFlyerLib.getInstance().logEvent(GameApp.instance, AFInAppEventType.PURCHASE, eventValues)
        } else if (!secondAFPurchase.value) {
            secondAFPurchase.value = true
            setBooleanValueByKey(KEY_REPORT_AF_PURCHASE2, true)
            eventValues[AF_SECOND_PURCHASE] = 1
            AppsFlyerLib.getInstance().logEvent(GameApp.instance, AFInAppEventType.PURCHASE, eventValues)
        }

        val bundle = bundleWithCommonParam()
        bundle.putInt(AFInAppEventParameterName.REVENUE, amount.toInt())
        bundle.putString(AFInAppEventParameterName.CONTENT_ID, purchaseId)
        bundle.putInt(AFInAppEventParameterName.QUANTITY, number)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.REFUND, bundle)
    }

    override fun onShopPurchase(sellId: Int, price: Int, priceType: Int) {
        val bundle = Bundle()
        bundle.putInt(AFInAppEventParameterName.REVENUE, price)
        bundle.putInt(PARAM_PURCHASE_TYPE, priceType)
        bundle.putString(AFInAppEventParameterName.CONTENT_ID, sellId.toString())
        firebaseAnalytics.logEvent(KEY_PURCHASE, bundle)
    }

    override fun onNewGame(mode: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(AF_NEW_USER, if (LoginManager.instance.newUser) 1 else 0)
        bundle.putInt(PARAM_GAME_MODE, mode)
        bundle.putInt(AFInAppEventParameterName.NEW_VERSION, getVersionCode())
        bundle.putString(AFInAppEventParameterName.CUSTOMER_USER_ID, GameApp.instance.getObjectId()?:"none")
        firebaseAnalytics.logEvent(KEY_START_GAME, bundle)
    }

    override fun onContinueGame(mode: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_GAME_MODE, mode)
        firebaseAnalytics.logEvent(KEY_CONTINUE_GAME, bundle)
    }

    override fun pk(win: Int, score: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_BATTLE_RESULT, win)
        bundle.putInt(PARAM_PVP_SCORE, score)
        firebaseAnalytics.logEvent(KEY_PK_GAME, bundle)
    }

    override fun battle(win: Int, mode: Int, stage: Int) {
        val bundle = bundleWithCommonParam()
        bundle.putInt(PARAM_BATTLE_RESULT, win)
        bundle.putInt(PARAM_GAME_MODE, mode)
        bundle.putInt(PARAM_STAGE, stage)
        firebaseAnalytics.logEvent(KEY_BATTLE_GAME, bundle)
    }

    private fun bundleWithCommonParam():Bundle {
        val bundle = Bundle()
        bundle.putInt(PARAM_KEY, AwardManager.key.value)
        bundle.putInt(PARAM_DIAMOND, AwardManager.diamond.value)
        bundle.putInt(PARAM_ELECTRIC, AwardManager.electric.value)
        bundle.putInt(AF_NEW_USER, if (LoginManager.instance.newUser) 1 else 0)
        bundle.putInt(AFInAppEventParameterName.NEW_VERSION, getVersionCode())
        bundle.putString(AFInAppEventParameterName.CUSTOMER_USER_ID, GameApp.instance.getObjectId()?:"none")
        return bundle
    }

    override fun onLoadAd() {
        val bundle = bundleWithCommonParam()
        firebaseAnalytics.logEvent(KEY_LOAD_AD, bundle)
    }

    override fun onPage(route: String) {
        val bundle = bundleWithCommonParam()
        bundle.putString(KEY_PAGE, route)
        firebaseAnalytics.logEvent(FirebaseAnalytics.Event.SCREEN_VIEW, bundle)
    }

    override fun onTalentUpgrade(level: Int) {
    }

    override fun onDungeonProgress(day: Int) {
    }
}