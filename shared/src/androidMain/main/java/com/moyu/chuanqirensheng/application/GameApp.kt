package com.moyu.chuanqirensheng.application

import android.app.Activity
import android.app.Application
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.LifecycleRegistry
import com.moyu.chuanqirensheng.application.inittask.AdjustTask
import com.moyu.chuanqirensheng.application.inittask.BillingTask
import com.moyu.chuanqirensheng.application.inittask.BuglyTask
import com.moyu.chuanqirensheng.application.inittask.ChannelTask
import com.moyu.chuanqirensheng.application.inittask.ConfigTask
import com.moyu.chuanqirensheng.application.inittask.DataStoreTask
import com.moyu.chuanqirensheng.application.inittask.DebugTask
import com.moyu.chuanqirensheng.application.inittask.LifecycleTask
import com.moyu.chuanqirensheng.application.inittask.MusicTask
import com.moyu.chuanqirensheng.application.inittask.ReportTask
import com.moyu.chuanqirensheng.application.inittask.RootCheckerTask
import com.moyu.chuanqirensheng.application.inittask.TTRewardAdTask
import com.moyu.chuanqirensheng.application.inittask.TimberTask
import com.moyu.chuanqirensheng.application.inittask.UncompressTask
import com.moyu.chuanqirensheng.cloud.loginsdk.GameSdkDefaultProcessor
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.ContextImpl
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import kotlinx.coroutines.runBlocking

class GameApp(
    gameSdkProcessor: GameSdkProcessor = GameSdkDefaultProcessor()
) : Application(), GameSdkProcessor by gameSdkProcessor {

    companion object {
        lateinit var instance: GameApp
    }

    lateinit var activity: ComponentActivity

    // 自定义生命周期，对齐旧代码的逻辑。回调与 registerActivityLifecycleCallbacks 保持一致
    private val lifececyleOwner = GameLifecycleOwner()
    var gameContext: Context = ContextImpl(lifececyleOwner)

    override fun onCreate() {
        super.onCreate()
        instance = this
        registerLifecycle()

        // 这个必须放到最前面，否则资源错误
        runBlocking {
            LanguageManager.init()
            TimberTask().execute(gameContext)

            UncompressTask().execute(gameContext)
            ConfigTask().execute(gameContext)
            MusicTask().execute(gameContext)
            AdjustTask().execute(gameContext)

            // Execute other tasks on the current thread
            ReportTask().execute(gameContext)
            DebugTask().execute(gameContext)
            LifecycleTask().execute(gameContext)
            BuglyTask().execute(gameContext)
            BillingTask().execute(gameContext)
            ChannelTask().execute(gameContext)
            RootCheckerTask().execute(gameContext)

            DataStoreTask().execute(gameContext)
            TTRewardAdTask().execute(gameContext)
        }
    }

    private fun registerLifecycle() {
        registerActivityLifecycleCallbacks(object : ActivityLifecycleCallbacks {
            override fun onActivityCreated(activity: Activity, savedInstanceState: Bundle?) {
                if (activity is GameActivity) {
                    lifececyleOwner.lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_CREATE)
                }
            }

            override fun onActivityStarted(activity: Activity) {
                if (activity is GameActivity) {
                    lifececyleOwner.lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_START)
                }
            }

            override fun onActivityResumed(activity: Activity) {
                lifececyleOwner.lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_RESUME)
            }

            override fun onActivityPaused(activity: Activity) {
                if (activity is GameActivity) {
                    lifececyleOwner.lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_PAUSE)
                }
            }

            override fun onActivityStopped(activity: Activity) {
                if (activity is GameActivity) {
                    lifececyleOwner.lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_STOP)
                }
            }

            override fun onActivitySaveInstanceState(activity: Activity, outState: Bundle) {
            }

            override fun onActivityDestroyed(activity: Activity) {
                if (activity is GameActivity) {
                    lifececyleOwner.lifecycleRegistry.handleLifecycleEvent(Lifecycle.Event.ON_DESTROY)
                }
            }
        })
    }
}

class GameLifecycleOwner() : LifecycleOwner {
    internal val lifecycleRegistry= LifecycleRegistry(this)

    override val lifecycle: Lifecycle
        get() = lifecycleRegistry
}
