package com.moyu.chuanqirensheng.platform.desktop

import com.moyu.chuanqirensheng.sub.ad.AdHolder
import com.moyu.chuanqirensheng.sub.ad.AdInterface
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.platform.reportManager
import com.moyu.chuanqirensheng.sub.language.LanguageManager
import com.moyu.core.AppWrapper
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import shared.generated.resources.Res
import shared.generated.resources.ad_sdk_init_failed
import shared.generated.resources.ad_still_playing

var playCount = 0

object AdPlayer : AdInterface {
    var isPlaying = false
    var lastPlayFailed = false

    init {
        println("=-== AdPlayer init")
        AdHolder.adInit = true
    }

    override fun playAd(adId: String, callback: () -> Unit) {
        if (!AdHolder.adInit) {
            AppWrapper.getStringKmp(Res.string.ad_sdk_init_failed).toast()
        } else {
            if (isPlaying) {
                AppWrapper.getStringKmp(Res.string.ad_still_playing).toast()
                return
            }
            isPlaying = true
            loadAdInternal(adId, callback)
        }
    }

    private fun loadAdInternal(adId: String, callback: () -> Unit) {
        playCount += 1

        AppWrapper.globalScope.launch(Dispatchers.Main) {
            reportManager().onLoadAd()
//
//            iosAdLoadBlock?.let { it.invoke { success ->
//                if (success) {
//                    lastPlayFailed = false
//                    playAdInternal(adId, callback)
//                } else {
//                    lastPlayFailed = true
//                    isPlaying = false
//                    "ad load fail".toast()
//                    lastPlayFailed = true
//                    isPlaying = false
//                }
//            } }
        }
    }
}

private fun playAdInternal(
    adId: String,
    callback: () -> Unit
) {
    MusicManager.muteByAd(true)

//    iosAdShowBlock?.let { it.invoke { success ->
//        if (success) {
//            AppWrapper.globalScope.launch(Dispatchers.Main) {
//                callback()
//                AwardManager.adNum.value += 1
//                reportManager().onAdCompletedAF(adId)
//            }
//            AdPlayer.isPlaying = false
//            MusicManager.muteByAd(false)
//        } else {
//            AdPlayer.isPlaying = false
//            MusicManager.muteByAd(false)
//            "ad show fail".toast()
//        }
//    } }
}