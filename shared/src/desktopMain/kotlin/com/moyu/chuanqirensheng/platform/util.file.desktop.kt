package com.moyu.chuanqirensheng.platform

import java.io.File
import java.io.FileInputStream
import java.io.FileOutputStream

import net.lingala.zip4j.ZipFile
import net.lingala.zip4j.exception.ZipException
import net.lingala.zip4j.model.ZipParameters
import net.lingala.zip4j.model.enums.CompressionLevel
import net.lingala.zip4j.model.enums.CompressionMethod
import net.lingala.zip4j.model.enums.EncryptionMethod

actual fun compressZip4j(zipFilePath: String, compressFilePath: String, password: String) {
    val zipFile = net.lingala.zip4j.ZipFile(File(zipFilePath))
    val zipParameters = ZipParameters()
    zipParameters.compressionMethod = CompressionMethod.DEFLATE
    zipParameters.compressionLevel = CompressionLevel.HIGHER // 压缩级别
    if (password.isNotEmpty()) {  //是否要加密(加密会影响压缩速度)
        zipParameters.isEncryptFiles = true
        zipParameters.encryptionMethod = EncryptionMethod.ZIP_STANDARD // 加密方式
    }
    zipFile.setPassword(password.toCharArray())
    zipFile.addFile(File(compressFilePath), zipParameters)
}

actual fun uncompressZip4j(zipFilePath: String, filePath: String, password: String) {
    val zipFile = ZipFile(File(zipFilePath))
    if (!zipFile.isValidZipFile) {   //检查输入的zip文件是否是有效的zip文件
        throw ZipException("压缩文件不合法,可能被损坏.")
    }
    if (zipFile.isEncrypted) {
        zipFile.setPassword(password.toCharArray())
    }
    zipFile.extractAll(filePath) //解压
}

//读取文本文件
actual fun openText(path: String?): ByteArray? {
    val b: ByteArray
    try {
        val fis = FileInputStream(path)
        b = ByteArray(fis.available())
        fis.read(b)
        fis.close()
        return b
    } catch (e: Exception) {
        e.printStackTrace()
    }
    return null
}

//保存文本文件
actual fun saveText(path: String, txt: ByteArray) {
    try {
        val fos = FileOutputStream(path)
        fos.write(txt)
        fos.close()
    } catch (e: java.lang.Exception) {
        e.printStackTrace()
    }
}
