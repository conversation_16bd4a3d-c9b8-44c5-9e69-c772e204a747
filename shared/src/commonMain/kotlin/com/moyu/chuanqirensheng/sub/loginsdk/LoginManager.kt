package com.moyu.chuanqirensheng.sub.loginsdk

import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.feature.server.ServerManager
import com.moyu.chuanqirensheng.feature.talent.TalentManager
import com.moyu.chuanqirensheng.platform.gameSdkDefaultProcessor
import com.moyu.chuanqirensheng.platform.getBuildFlavor
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.sub.anticheat.AntiCheatManager
import com.moyu.chuanqirensheng.sub.saver.getFootPrint
import com.moyu.chuanqirensheng.util.AESUtil
import com.moyu.chuanqirensheng.util.AdUtil
import com.moyu.chuanqirensheng.util.getVersionCode
import com.moyu.core.util.RANDOM

class LoginManager {
    companion object {

        var instance = LoginManager()
    }

    var newUser: Boolean = false
    var loginData = mutableStateOf(LoginData(0L, verified = true, showDialog = false))

    fun canShowAifadian(): Boolean {
        return if (!hasGoogleService()){
            loginData.value.canShowAifadian
        } else {
            // google版，这里是true，不是说有爱发电，而是说有付费商品
            true
        }
    }

    fun getShareCode(): String {
        return AdUtil.simpleEncodeText(gameSdkDefaultProcessor().getObjectId()!!)
    }

    suspend fun getLoginUser(): LoginUser {
        return LoginUser(
            versionCode = getVersionCode(),
            userId = gameSdkDefaultProcessor().getObjectId() ?: "",
            userName = gameSdkDefaultProcessor().getUserName()?: "",
            footPrint = getFootPrint(),
            randomInt = RANDOM.nextInt(),
            isCheating = AntiCheatManager.isCheating(),
            platformChannel = platformChannel(),
            buildFlavor = getBuildFlavor(),
            signature = AESUtil.getSignature(),
            serverId = ServerManager.getSavedServerId()
        )
    }
}
