package com.moyu.chuanqirensheng.screen.effect

import androidx.compose.animation.core.CubicBezierEasing
import androidx.compose.animation.core.Easing
import androidx.compose.animation.core.LinearEasing
import androidx.compose.animation.core.TweenSpec
import androidx.compose.animation.core.animateIntAsState
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.*
import com.moyu.chuanqirensheng.ui.theme.turnEffectWidth
import com.moyu.chuanqirensheng.ui.theme.upgradeEffectWidth
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.core.AppWrapper
import com.moyu.core.model.skill.Skill
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

val VeryFastOutSlowInEasing: Easing = CubicBezierEasing(0f, 1f, 1f, 0f)

val newTurnEffectState = mutableStateOf<Pair<String, Int>?>(null)
val dialogEffectState = mutableStateOf<Pair<String, Int>?>(null)
val castSkillEffectState = mutableIntStateOf(0)


val winBattleEffect = Pair("reward_", 16)
val loseBattleEffect = Pair("effect11_", 6)
val starUpEffect = Pair("starup_", 7)
val turnEffect = Pair("next_", 8)

val cardEffects = mutableStateOf<Skill?>(null)

fun restartEffect(state: MutableState<Pair<String, Int>?>, effect: Pair<String, Int>) {
    AppWrapper.globalScope.launch {
        state.value = null
        delay(200)
        state.value = effect
    }
}

@Composable
fun DialogUpgradeEffect(modifier: Modifier) {
    val enabled = dialogEffectState.value != null
    val effectIndex by animateIntAsState(
        targetValue = when {
            enabled -> dialogEffectState.value!!.second
            else -> 1
        },
        animationSpec = TweenSpec(
            durationMillis = if (enabled) 500 else 0,
            easing = LinearEasing
        ),
        finishedListener = {
            dialogEffectState.value = null
        }, label = ""
    )
    dialogEffectState.value?.let { effect ->
        Image(
            modifier = modifier.width(upgradeEffectWidth),
            contentScale = ContentScale.FillWidth,
            painter = kmpPainterResource("${effect.first}${effectIndex}"),
            contentDescription = null
        )
    }
}


@Composable
fun NewTurnEffect() {
    val enabled = newTurnEffectState.value != null
    val effectIndex by animateIntAsState(
        targetValue = when {
            enabled -> newTurnEffectState.value!!.second
            else -> 1
        },
        animationSpec = TweenSpec(
            durationMillis = if (enabled) 350 else 0,
            easing = LinearEasing
        ),
        finishedListener = {
            newTurnEffectState.value = null
        }, label = ""
    )
    newTurnEffectState.value?.let { effect ->
        Image(
            modifier = Modifier
                .width(turnEffectWidth)
                .graphicsLayer {
                    clip = false
                },
            contentScale = ContentScale.FillWidth,
            painter = kmpPainterResource("${effect.first}${effectIndex}"),
            contentDescription = null
        )
    }
}