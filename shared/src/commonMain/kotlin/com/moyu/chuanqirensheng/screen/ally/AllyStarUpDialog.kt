package com.moyu.chuanqirensheng.screen.ally

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CommonBar
import com.moyu.chuanqirensheng.screen.common.PanelLayout
import com.moyu.chuanqirensheng.screen.common.PanelSize
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.screen.equip.MainPropertyLine
import com.moyu.chuanqirensheng.ui.theme.imageLargePlus
import com.moyu.chuanqirensheng.ui.theme.padding120
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding36
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.util.kmpPainterResource
import com.moyu.core.AppWrapper
import com.moyu.core.model.Ally
import com.moyu.core.model.role.Role
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.ally_star_up_tips
import shared.generated.resources.common_card_empty
import shared.generated.resources.common_card_line
import shared.generated.resources.do_star_up
import shared.generated.resources.hero_star_up_tips
import shared.generated.resources.star_max

@Composable
fun AllyStarUpDialog(show: MutableState<Ally?>) {
    show.value?.let { ally ->
        val nextAlly = repo.gameCore.getAllyPool()
            .firstOrNull { it.mainId == ally.mainId && it.star == ally.star + 1 }
        val role = BattleManager.getRoleByAlly(ally)
        val nextRole = nextAlly?.let { BattleManager.getRoleByAlly(it) }
        EmptyDialog(onDismissRequest = { show.value = null }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                nextRole?.let {
                    AllySkillPanel(Modifier, ally, nextAlly, role, nextRole) {
                        Dialogs.allyStarUpDialog.value = null
                    }
                    Spacer(modifier = Modifier.size(padding14))
                    DoAllyStarUpView(ally = ally)
                }
            }
        }
    }
}

@Composable
fun DoAllyStarUpView(ally: Ally) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.SpaceAround
    ) {
        val maxStar = ally.star >= ally.starLimit
        val buttonText = if (maxStar) AppWrapper.getStringKmp(Res.string.star_max) else AppWrapper.getStringKmp(
                Res.string.do_star_up
            )
        val enabled =
            !ally.peek && ally.starUpNum != 0 && ally.num >= ally.starUpNum && !maxStar && AwardManager.diamond.value >= ally.starUpRes
        GameButton(text = buttonText,
            buttonStyle = ButtonStyle.Orange,
            buttonSize = ButtonSize.Big,
            enabled = enabled,
            onClick = {
                repo.allyManager.upgrade(ally)?.let {
                    Dialogs.allyDetailDialog.value = it
                    if (it.star == it.starLimit) {
                        Dialogs.allyStarUpDialog.value = null
                    } else {
                        Dialogs.allyStarUpDialog.value = it
                    }
                }
            })
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun AllySkillPanel(modifier: Modifier, ally: Ally, nextAlly: Ally, role: Role, nextRole: Role, onClose:()->Unit) {
    PanelLayout(modifier, PanelSize.Normal, showClose = true, onClose = onClose) {
        Column(
            modifier = Modifier.fillMaxSize(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            Text(
                text = if (ally.isHero()) stringResource(Res.string.hero_star_up_tips, ally.star, nextAlly.star)
                else stringResource(Res.string.ally_star_up_tips, ally.star, nextAlly.star),
                style = MaterialTheme.typography.h1,
                color = Color.Black
            )
            Spacer(modifier = Modifier.size(padding4))
            Row(verticalAlignment = Alignment.CenterVertically) {
                AllyHeadImage(Modifier.size(imageLargePlus), ally)
                CommonBar(
                    modifier = Modifier.size(padding120, padding22),
                    currentValue = ally.num,
                    maxValue = ally.starUpNum,
                    fullRes = Res.drawable.common_card_line,
                    emptyRes = Res.drawable.common_card_empty,
                    textColor = Color.White,
                    style = MaterialTheme.typography.h6
                )
            }
            Spacer(modifier = Modifier.size(padding36))
            FlowRow(
                horizontalArrangement = Arrangement.spacedBy(padding4),
                verticalArrangement = Arrangement.Center,
                overflow = FlowRowOverflow.Visible,
                maxItemsInEachRow = 3
            ) {
                nextRole.getCurrentProperty().MainPropertyLine(
                    originProperty = role.getCurrentProperty(),
                    textStyle = MaterialTheme.typography.h4,
                    showBoost = true
                )
            }
        }
    }
}

@Composable
fun AllyHeadImage(modifier: Modifier, ally: Ally) {
    Box(modifier = modifier) {
        Image(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding4)
                .clip(RoundedCornerShape(50)),
            painter = kmpPainterResource(ally.getRace().getHeadIcon()),
            contentDescription = null
        )
    }
}