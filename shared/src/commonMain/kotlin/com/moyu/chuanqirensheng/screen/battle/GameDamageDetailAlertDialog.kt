package com.moyu.chuanqirensheng.screen.battle

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.*
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.core.model.info.BattleInfo


@Composable
fun GameDamageDetailAlertDialog(
    alertDialog: MutableState<Boolean>,
    battleInfo: BattleInfo
) {
    if (alertDialog.value) {
        PanelDialog(
            onDismissRequest = {
                alertDialog.value = false
            }
        ) {
            Column(modifier = Modifier.fillMaxWidth()
                .padding(horizontal = padding10)
                .verticalScroll(rememberScrollState())) {
                Text(
                    text = battleInfo.damageData?.damageInfo?: "",
                    style = MaterialTheme.typography.h4,
                    color = Color.Black
                )
                Text(
                    text = stringResource(Res.string.normal_shield_blocks) + (battleInfo.damageData?.damageValue?.normalShieldBlockedDamage
                        ?: 0),
                    style = MaterialTheme.typography.h4,
                    color = Color.Black
                )
                Text(
                    text = stringResource(Res.string.all_shield_blocks) + (battleInfo.damageData?.damageValue?.allShieldBlockedDamage
                        ?: 0),
                    style = MaterialTheme.typography.h4,
                    color = Color.Black
                )
                Text(
                    text = stringResource(Res.string.real_damage) + (battleInfo.damageData?.damageValue?.finalDamage ?: 0),
                    style = MaterialTheme.typography.h4,
                    color = Color.Black
                )
            }
        }
    }
}