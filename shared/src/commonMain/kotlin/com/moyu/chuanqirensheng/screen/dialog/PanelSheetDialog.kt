package com.moyu.chuanqirensheng.screen.dialog

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.RowScope
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableIntState
import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.common.PanelLayout
import com.moyu.chuanqirensheng.screen.common.PanelSize
import com.moyu.chuanqirensheng.screen.common.TagView
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.tabButtonHeight
import com.moyu.chuanqirensheng.ui.theme.tabButtonWidth

@Composable
fun PanelSheetDialog(
    onDismissRequest: () -> Unit = EMPTY_DISMISS,
    titles: List<String>,
    reds: List<Boolean> = listOf(false, false, false, false, false, false),
    selected: MutableIntState = remember {
        mutableIntStateOf(0)
    },
    contentBelow: @Composable RowScope.(Int) -> Unit = {},
    content: @Composable BoxScope.(Int) -> Unit
) {
    EmptyDialog(onDismissRequest = onDismissRequest) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            PanelLayout(size = PanelSize.Normal, showClose = true, onClose = onDismissRequest) {
                content(selected.intValue)
            }
            Row(
                modifier = Modifier
                    .width(PanelSize.Normal.width)
                    .padding(horizontal = padding12)
                    .graphicsLayer {
                        translationY = -padding12.toPx()
                    },
                horizontalArrangement = if (titles.size >= 5) Arrangement.SpaceEvenly else Arrangement.spacedBy(
                    padding4
                )
            ) {
                titles.forEachIndexed { index, title ->
                    EffectButton(onClick = {
                        selected.intValue = index
                    }) {
                        TagView(
                            modifier = Modifier
                                .size(tabButtonWidth, tabButtonHeight),
                            title,
                            selected = index == selected.intValue,
                            reds[index]
                        )
                    }
                }
            }
            Spacer(modifier = Modifier.size(padding16))
            Row(Modifier.fillMaxWidth(), horizontalArrangement = Arrangement.SpaceEvenly) {
                contentBelow(selected.intValue)
            }
        }
    }
}