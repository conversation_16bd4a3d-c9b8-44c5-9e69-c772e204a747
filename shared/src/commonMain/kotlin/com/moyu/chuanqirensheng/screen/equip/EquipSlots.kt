package com.moyu.chuanqirensheng.screen.equip

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.getEquipTypeTips
import com.moyu.chuanqirensheng.logic.toEquipTypeRes
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.effect.GifView
import com.moyu.chuanqirensheng.screen.effect.equipGif
import com.moyu.chuanqirensheng.screen.skill.SingleEquipView
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.ui.theme.padding4
import com.moyu.chuanqirensheng.ui.theme.padding6
import shared.generated.resources.Res
import shared.generated.resources.item_frame_new


@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EquipSlots() {
    val itemSize = ItemSize.Large
    val equips = BattleManager.getGameEquips().filter { it.isEquipReplaceable() }
    FlowRow(
        horizontalArrangement = Arrangement.spacedBy(padding4),
        verticalArrangement = Arrangement.spacedBy(padding4),
        overflow = FlowRowOverflow.Visible,
        maxItemsInEachRow = 4
    ) {
        repeat(8) { index ->
            Box(
                modifier = Modifier.width(itemSize.frameSize), contentAlignment = Alignment.Center
            ) {
                val gif = remember {
                    mutableStateOf(false)
                }
                equips.firstOrNull { it.type == index + 1 }?.let { skill ->
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Box(contentAlignment = Alignment.Center) {
                            SingleEquipView(
                                equipment = skill,
                                itemSize = itemSize,
                                showName = true,
                                textColor = Color.Black
                            )
                            GifView(
                                modifier = Modifier
                                    .size(itemSize.itemSize)
                                    .graphicsLayer {
                                        translationX = padding10.toPx()
                                        translationY = padding10.toPx()
                                    }, gif.value, equipGif.count, equipGif.gif
                            )
                        }
                    }
                } ?: run {
                    gif.value = true
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        IconView(
                            modifier = Modifier.width(itemSize.frameSize),
                            itemSize = itemSize,
                            itemExtraPadding = padding6,
                            resZIndex = 99f,
                            res = (index + 1).toEquipTypeRes(),
                            frame = Res.drawable.item_frame_new,
                        ) {
                            (index + 1).getEquipTypeTips().toast()
                        }
                    }
                }
            }
        }
    }
}