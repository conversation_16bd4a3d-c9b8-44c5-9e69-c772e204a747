package com.moyu.chuanqirensheng.screen.role

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.ally.AllyPanel
import com.moyu.chuanqirensheng.screen.ally.AllyPropView
import com.moyu.chuanqirensheng.screen.dialog.EmptyDialog
import com.moyu.chuanqirensheng.ui.theme.padding19
import com.moyu.core.model.role.Role

@Composable
fun RoleDetailDialog(show: MutableState<Role?>) {
    show.value?.let { role ->
        val ally = repo.gameCore.getAllyPool().first { it.id == role.getRace().id }
        EmptyDialog(onDismissRequest = { show.value = null }) {
            Column(
                Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                AllyPanel(ally = ally, role = role) {
                    show.value = null
                }
                Spacer(modifier = Modifier.size(padding19))
                AllyPropView(newRole = role)
            }
        }
    }
}