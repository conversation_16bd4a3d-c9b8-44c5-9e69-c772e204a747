package com.moyu.chuanqirensheng.screen.common

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.ScrollState
import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectVerticalDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxHeight
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import kotlinx.coroutines.launch
@Composable
fun VerticalScrollbar(
    modifier: Modifier = Modifier,
    scrollState: ScrollState,
) {
    var handleHeight by remember { mutableStateOf(0f) }
    val proportion = remember(scrollState.value, scrollState.maxValue) {
        scrollState.value.toFloat() / scrollState.maxValue.toFloat()
    }
    val coroutineScope = rememberCoroutineScope()
    Box(modifier) {
        Canvas(modifier = Modifier
            .fillMaxWidth()
            .fillMaxHeight()
            .background(Color.Gray.copy(alpha = 0.3f))
        ) {
            val canvasHeight = size.height
            // Ensure the handle is at least 48 pixels high for better touch support
            handleHeight = maxOf(canvasHeight * (canvasHeight / (scrollState.maxValue + canvasHeight)), 48f)
            // Calculate the top offset for the handle
            val handleOffset = (canvasHeight - handleHeight) * proportion
            drawRoundRect(
                color = Color.Gray,
                topLeft = androidx.compose.ui.geometry.Offset(0f, handleOffset),
                size = androidx.compose.ui.geometry.Size(size.width, handleHeight),
            )
        }

        Box(
            Modifier
                .fillMaxWidth()
                .fillMaxHeight()
                .pointerInput(Unit) {
                    detectVerticalDragGestures { change, dragAmount ->
                        coroutineScope.launch {
                            val maxScroll = scrollState.maxValue.toFloat()
                            val newScrollValue =
                                (scrollState.value + dragAmount).coerceIn(0f, maxScroll)
                            scrollState.scrollTo(newScrollValue.toInt())
                        }
                        change.consume()
                    }
                }
        )
    }
}
