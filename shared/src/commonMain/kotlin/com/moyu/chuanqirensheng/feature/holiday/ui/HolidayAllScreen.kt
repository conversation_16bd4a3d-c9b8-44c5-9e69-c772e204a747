package com.moyu.chuanqirensheng.feature.holiday.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.B50
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.painterResource
import shared.generated.resources.Res
import shared.generated.resources.holiday_bg
import shared.generated.resources.holiday_tab1
import shared.generated.resources.holiday_tab2
import shared.generated.resources.holiday_tab3
import shared.generated.resources.holiday_tab4
import shared.generated.resources.holiday_tab5
import shared.generated.resources.holiday_title


@Composable
fun HolidayAllScreen() {
    val listTabItems = remember {
        mutableStateListOf(
            AppWrapper.getStringKmp(Res.string.holiday_tab1),
            AppWrapper.getStringKmp(Res.string.holiday_tab2),
            AppWrapper.getStringKmp(Res.string.holiday_tab3),
            AppWrapper.getStringKmp(Res.string.holiday_tab4),
            AppWrapper.getStringKmp(Res.string.holiday_tab5),
        )
    }
    val pagerState = rememberPagerState(initialPage = 0) {
        listTabItems.size
    }
    LaunchedEffect(Unit) {
        SellManager.init()
        HolidayManager.init(true)
    }
    GameBackground(
        title = AppWrapper.getStringKmp(Res.string.holiday_title),
        bgMask = B50,
        background = Res.drawable.holiday_bg
    ) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            NavigationTab(
                pageState = pagerState,
                titles = listTabItems,
                redIcons = HolidayManager.getRedIcons()
            )
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                when (page) {
                    0 -> HolidaySignPage()
                    1 -> HolidayQuestPage()
                    2 -> HolidayLotteryPage()
                    3 -> HolidaySellPage()
                    else -> HolidayRankPage()// 加密，不然可能有风险
                }
            }
        }
    }
}