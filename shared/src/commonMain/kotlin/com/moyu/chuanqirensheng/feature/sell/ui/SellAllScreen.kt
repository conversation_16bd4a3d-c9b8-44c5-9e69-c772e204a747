package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.mutableStateListOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.platform.getPlatform
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.sub.ad.AdHolder
import com.moyu.chuanqirensheng.ui.theme.padding14
import com.moyu.chuanqirensheng.util.Platform
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.free_goods
import shared.generated.resources.key_goods
import shared.generated.resources.menu4
import shared.generated.resources.packages
import shared.generated.resources.sell_pool
import shared.generated.resources.special_goods


@Composable
fun SellAllScreen(initTab: Int = 0) {
    val listTabItems = remember {
        if (getPlatform() == Platform.Desktop) {
            mutableStateListOf(
                AppWrapper.getStringKmp(Res.string.sell_pool),
                AppWrapper.getStringKmp(Res.string.free_goods),
//                AppWrapper.getStringKmp(Res.string.packages),
                AppWrapper.getStringKmp(Res.string.special_goods),
//                AppWrapper.getStringKmp(Res.string.key_goods),
            )
        } else {
            mutableStateListOf(
                AppWrapper.getStringKmp(Res.string.sell_pool),
                AppWrapper.getStringKmp(Res.string.free_goods),
                AppWrapper.getStringKmp(Res.string.packages),
                AppWrapper.getStringKmp(Res.string.special_goods),
                AppWrapper.getStringKmp(Res.string.key_goods),
            )
        }
    }
    val pagerState = rememberPagerState(initialPage = minOf(initTab, listTabItems.size - 1)) {
        listTabItems.size
    }
    GameBackground(title = stringResource(Res.string.menu4)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier
                    .weight(1f)
                    .fillMaxWidth(),
                state = pagerState,
            ) { page ->
                if (getPlatform() == Platform.Desktop) {
                    when (page) {
                        0 -> DrawPage()
                        1 -> SellPage(listOf(1, 31))
                        2 -> SellPage(listOf(2))
                    }
                } else {
                    when (page) {
                        0 -> DrawPage()
                        1 -> SellPage(listOf(1, 31))
                        2 -> SellPage(listOf(2))
                        3 -> SellPage(listOf(3))
                        else -> SellPage(listOf(4))
                    }
                }
            }
            NavigationTab(
                modifier = Modifier.graphicsLayer {
                    translationY = -padding14.toPx()
                },
                pageState = pagerState,
                titles = listTabItems,
                redIcons = List(listTabItems.size) { index ->
                    SellManager.getRedFree(index)
                }
            )
        }
    }
}