package com.moyu.chuanqirensheng.feature.vip.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.*
import com.moyu.chuanqirensheng.logic.getQualityFrame
import com.moyu.chuanqirensheng.screen.award.AwardList
import com.moyu.chuanqirensheng.screen.award.defaultParam
import com.moyu.chuanqirensheng.screen.common.IconView
import com.moyu.chuanqirensheng.screen.common.ItemSize
import com.moyu.chuanqirensheng.screen.dialog.PanelDialog
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.chuanqirensheng.util.kmpDrawableResource
import com.moyu.core.model.Vip
import com.moyu.core.model.toAward

@Composable
fun VipDialog(show: MutableState<Vip?>) {
    show.value?.let {
        PanelDialog(onDismissRequest = {
            show.value = null
        }) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(padding10),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    stringResource(Res.string.awards),
                    style = MaterialTheme.typography.h1,
                    color = Color.Black
                )
                Spacer(modifier = Modifier.size(padding10))
                VipItem(vip = it)
            }
        }
    }
}

@Composable
fun VipItem(vip: Vip, itemSize: ItemSize = ItemSize.Large, callback: (() -> Unit)? = null) {
    if (vip.effectType == 1) {
        val award = vip.toAward()
        AwardList(
            Modifier,
            award = award,
            param = defaultParam.copy(
                peek = true,
                textColor = Color.White,
                itemSize = itemSize,
                showName = false,
                frameDrawable = 3.getQualityFrame(),
                callback = callback,
            ),
        )
    } else {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            IconView(res = kmpDrawableResource(vip.pic),
                frame = 3.getQualityFrame(),
                resZIndex = 99f,
                itemSize = itemSize,
                callback = { callback?.invoke() })
            Spacer(modifier = Modifier.size(padding10))
            Text(text = vip.desc, style = MaterialTheme.typography.h3, color = Color.Black)
        }
    }
}
