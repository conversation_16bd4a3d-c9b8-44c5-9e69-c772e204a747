package com.moyu.chuanqirensheng.feature.pvp.ui

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.api.getRanks
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.pvp.MAX_PVP2_NUM
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.feature.rank.LAST_PVP2_TYPE
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.feature.router.PVP2_CHOOSE_ENEMY_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_QUEST_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP2_RANK_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.platform.platformChannel
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.sub.datastore.json
import com.moyu.chuanqirensheng.ui.theme.B65
import com.moyu.chuanqirensheng.ui.theme.W30
import com.moyu.chuanqirensheng.ui.theme.padding0
import com.moyu.chuanqirensheng.ui.theme.padding100
import com.moyu.chuanqirensheng.ui.theme.padding12
import com.moyu.chuanqirensheng.ui.theme.padding22
import com.moyu.chuanqirensheng.ui.theme.padding3
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.chuanqirensheng.util.getCurrentTime
import com.moyu.chuanqirensheng.util.isBetween23_45And00_15
import com.moyu.chuanqirensheng.util.isNetTimeValid
import com.moyu.core.AppWrapper
import kotlinx.coroutines.delay
import kotlinx.serialization.builtins.ListSerializer
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.arena_time_tips
import shared.generated.resources.bg_3
import shared.generated.resources.net_error_retry
import shared.generated.resources.pvp2
import shared.generated.resources.pvp2_rank
import shared.generated.resources.pvp_entrance_icon
import shared.generated.resources.pvp_quest2
import shared.generated.resources.pvp_quest_icon
import shared.generated.resources.pvp_rank_icon

@Composable
fun Pvp2EntryScreen() {
    LaunchedEffect(Unit) {
        // 进这个页面就需要刷一下排行榜，需要领取排名任务
        Pvp2Manager.init()
        try {
            if (!isBetween23_45And00_15(getCurrentTime())) {
                if (lastPvp2Ranks.value.isEmpty()) {
                    delay(200)
                    getRanks(
                        platformChannel(),
                        LAST_PVP2_TYPE
                    ).let {
                        lastPvp2Ranks.value =
                            json.decodeFromString(ListSerializer(RankData.serializer()), it.message)
                    }
                }
            }
        } catch (e: Exception) {
//            Timber.e(e)
            AppWrapper.getStringKmp(Res.string.net_error_retry).toast()
        }
    }
    GameBackground(
        title = stringResource(Res.string.pvp2),
        bgMask = B65,
        background = Res.drawable.bg_3
    ) {
        Pvp2TopDataRow(Modifier
            .fillMaxWidth()
            .padding(top = padding6)
            .background(W30))
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(horizontal = padding12, vertical = padding100),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.End)
                    .padding(end = padding22),
                stringResource(Res.string.pvp2),
                Res.drawable.pvp_entrance_icon,
                red = {
                    Pvp2Manager.pkNumToday.value < MAX_PVP2_NUM
                }
            ) {
                if (isBetween23_45And00_15(getCurrentTime())) {
                    AppWrapper.getStringKmp(Res.string.arena_time_tips).toast()
                } else {
                    pvp2Ranks.value = emptyList()
                    goto(PVP2_CHOOSE_ENEMY_SCREEN)
                }
            }
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.Start)
                    .padding(start = padding0),
                stringResource(Res.string.pvp_quest2), Res.drawable.pvp_quest_icon,
                red = {
                    QuestManager.pvp2Tasks.any {
                        QuestManager.getTaskDoneFlow(it)
                                && !it.opened
                    }
                }
            ) {
                if (isNetTimeValid()) {
                    if (isBetween23_45And00_15(getCurrentTime())) {
                        AppWrapper.getStringKmp(Res.string.arena_time_tips).toast()
                    } else {
                        goto(PVP2_QUEST_SCREEN)
                    }
                }
            }
            SingleMapItem(
                Modifier
                    .weight(1f)
                    .align(Alignment.End)
                    .padding(end = padding3),
                stringResource(Res.string.pvp2_rank),
                Res.drawable.pvp_rank_icon,
                red = {
                    false
                }
            ) {
                if (isBetween23_45And00_15(getCurrentTime())) {
                    AppWrapper.getStringKmp(Res.string.arena_time_tips).toast()
                } else {
                    goto(PVP2_RANK_SCREEN)
                }
            }
        }
    }
}