package com.moyu.chuanqirensheng.feature.draw.ui

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.feature.router.DRAW_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.vip.DrawAwardManager.getDrawLevel
import com.moyu.chuanqirensheng.feature.vip.DrawAwardManager.isThisLevelGained
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.EffectButton
import com.moyu.chuanqirensheng.screen.effect.ForeverGif
import com.moyu.chuanqirensheng.screen.effect.packageGif
import com.moyu.chuanqirensheng.screen.resource.CurrentHistoryCouponPoint
import com.moyu.chuanqirensheng.ui.theme.imageHugeLite
import com.moyu.chuanqirensheng.ui.theme.imageSmall
import org.jetbrains.compose.resources.painterResource
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.chest_coupon_history
import shared.generated.resources.history_coupon
import shared.generated.resources.red_icon


@Composable
fun HistoryCouponItem() {
    Row(
        Modifier.fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.SpaceEvenly
    ) {
        CurrentHistoryCouponPoint()
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            EffectButton(onClick = {
                goto(DRAW_SCREEN)
            }) {
                if (Dialogs.drawResultDialog.value == null) {
                    ForeverGif(
                        modifier = Modifier
                            .size(imageHugeLite).scale(1.5f),
                        resource = packageGif.gif,
                        num = packageGif.count,
                        needGap = false
                    )
                }
                Image(
                    modifier = Modifier.size(imageHugeLite),
                    painter = painterResource(Res.drawable.chest_coupon_history),
                    contentDescription = stringResource(Res.string.history_coupon)
                )
                val pool = repo.gameCore.getDrawAwardPool()
                val drawLevel = getDrawLevel()
                val enabled = if (drawLevel == 0) false else (0..<getDrawLevel()).any {
                    !isThisLevelGained(pool[it])
                }
                if (enabled) {
                    Image(
                        modifier = Modifier
                            .align(Alignment.TopEnd)
                            .size(imageSmall),
                        painter = painterResource(Res.drawable.red_icon),
                        contentDescription = null
                    )
                }
            }
        }
    }
}