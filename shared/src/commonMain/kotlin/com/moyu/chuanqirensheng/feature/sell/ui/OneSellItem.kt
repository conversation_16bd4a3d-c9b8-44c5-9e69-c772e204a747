package com.moyu.chuanqirensheng.feature.sell.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.scale
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.gift.GiftManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayLotteryManager
import com.moyu.chuanqirensheng.feature.holiday.HolidayManager
import com.moyu.chuanqirensheng.feature.lottery.LotteryManager
import com.moyu.chuanqirensheng.feature.monthcard.MonthCardManager
import com.moyu.chuanqirensheng.feature.newTask.SevenDayManager
import com.moyu.chuanqirensheng.feature.sell.SellManager
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.platform.billPrepay
import com.moyu.chuanqirensheng.platform.hasBilling
import com.moyu.chuanqirensheng.platform.hasGoogleService
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.ButtonType
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.sub.ad.AdHolder
import com.moyu.chuanqirensheng.sub.ad.KEY_BUY_AD_ITEM
import com.moyu.chuanqirensheng.sub.loginsdk.LoginManager
import com.moyu.chuanqirensheng.ui.theme.padding16
import com.moyu.core.AppWrapper
import com.moyu.core.aiFaDian
import com.moyu.core.model.Sell
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.cant_get_yet
import shared.generated.resources.confirm_buy
import shared.generated.resources.confirm_buy2
import shared.generated.resources.diamond_not_enough
import shared.generated.resources.go_and_get
import shared.generated.resources.key_not_enough
import shared.generated.resources.lottery_run_out
import shared.generated.resources.pvp_diamond_not_enough
import shared.generated.resources.real_money_not_enough
import shared.generated.resources.real_money_not_enough_content
import shared.generated.resources.sold_out


@Composable
fun OneSellItem(sell: Sell, scale: Float = 1f) {
    Column(horizontalAlignment = Alignment.CenterHorizontally) {
        SingleNormalChest(Modifier.scale(scale), sell) {
            SellButton(sell = sell)
        }
        Spacer(modifier = Modifier.size(padding16))
    }
}

@Composable
fun SellButton(sell: Sell, showRealMoney: Boolean = false) {
    GameButton(
        text = if (sell.storage <= 0) stringResource(Res.string.sold_out) else {
            if (sell.isAifadian()) {
                if (hasBilling()) {
                    if (hasGoogleService()) {
                        sell.priceDollar.toString()
                    } else {
                        sell.price.toString()
                    }
                } else if (showRealMoney) {
                    sell.price.toString()
                } else {
                    AppWrapper.getStringKmp(Res.string.go_and_get)
                }
            } else sell.price.toString()
        },
        buttonSize = ButtonSize.MediumMinus,
        buttonStyle = ButtonStyle.Orange,
        buttonType = if (sell.storage <= 0) ButtonType.Normal
        else if (sell.isAifadian()) if (showRealMoney) ButtonType.RealMoney else ButtonType.AiFaDian
        else if (sell.isAd()) ButtonType.Ad
        else if (sell.isKeyMoney()) ButtonType.Key
        else if (sell.isPvpMoney()) ButtonType.Pvp
        else ButtonType.Diamond,
        enabled = sell.storage > 0,
        onClick = {
            if (sell.storage <= 0) {
                AppWrapper.getStringKmp(Res.string.sold_out).toast()
            } else if (sell.isHoliday() && !HolidayLotteryManager.canDoCheap()) {
                AppWrapper.getStringKmp(Res.string.lottery_run_out).toast()
            } else if (sell.isDiamondMoney() && AwardManager.diamond.value < sell.price) {
                GiftManager.onDiamondNotEnough()
                AppWrapper.getStringKmp(Res.string.diamond_not_enough).toast()
            } else if (sell.isPvpMoney() && AwardManager.pvpDiamond.value < sell.price) {
                GiftManager.onPvpDiamondNotEnough()
                AppWrapper.getStringKmp(Res.string.pvp_diamond_not_enough).toast()
            } else if (sell.isKeyMoney() && AwardManager.key.value < sell.price) {
                GiftManager.onKeyNotEnough()
                AppWrapper.getStringKmp(Res.string.key_not_enough).toast()
            } else if (sell.isAifadianTower() && !hasBilling() && AwardManager.realMoney.value < sell.price) {
                if (LoginManager.instance.canShowAifadian()) {
                    Dialogs.alertDialog.value =
                        CommonAlert(
                            title = AppWrapper.getStringKmp(Res.string.real_money_not_enough),
                            content = AppWrapper.getStringKmp(Res.string.real_money_not_enough_content),
                            onConfirm = {
//                                val uri: Uri = Uri.parse(aiFaDian)
//                                val intent = Intent(Intent.ACTION_VIEW, uri)
//                                ContextCompat.startActivity(
//                                    GameApp.instance.activity,
//                                    intent,
//                                    Bundle()
//                                )
                            })
                } else {
                    AppWrapper.getStringKmp(Res.string.real_money_not_enough).toast()
                }
            } else {
                if (sell.isAd()) {
                    AppWrapper.globalScope.launch(Dispatchers.Main) {
                        AdHolder.playAd(KEY_BUY_AD_ITEM) {
                            if (sell.isNewTaskPackage()) {
                                SevenDayManager.openPackage(sell)
                            } else if (sell.isHoliday()) {
                                HolidayManager.openPackage(sell)
                            } else if (sell.isTower()) {
                                TowerManager.openPackage(sell)
                            } else if (sell.isMonthCard()) {
                                MonthCardManager.openPackage(sell)
                            } else if (sell.isLotteryGift()) {
                                LotteryManager.openPackage(sell)
                            } else {
                                SellManager.openSellChest(sell)
                            }
                        }
                    }
                } else if (sell.isAifadian()) {
                    if (hasBilling()) {
                        // 谷歌内购
                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                            billPrepay(sell) {
                                AppWrapper.globalScope.launch(Dispatchers.Main) {
                                    if (sell.isNewTaskPackage()) {
                                        SevenDayManager.openPackage(sell)
                                    } else if (sell.isHoliday()) {
                                        HolidayManager.openPackage(sell)
                                    } else if (sell.isTower()) {
                                        TowerManager.openPackage(sell)
                                    } else if (sell.isLotteryGift()) {
                                        LotteryManager.openPackage(sell)
                                    } else if (sell.isMonthCard()) {
                                        MonthCardManager.openPackage(sell)
                                    } else {
                                        SellManager.openSellChest(sell)
                                    }
                                }
                            }
                        }
                    } else {
                        if (sell.isAifadianTower()) {
                            Dialogs.alertDialog.value =
                                CommonAlert(
                                    title = AppWrapper.getStringKmp(Res.string.confirm_buy),
                                    content = AppWrapper.getStringKmp(Res.string.confirm_buy2) + sell.name + "?",
                                    onConfirm = {
                                        // todo 爬塔的爱发电sell
                                        AppWrapper.globalScope.launch(Dispatchers.Main) {
                                            if (sell.isNewTaskPackage()) {
                                                SevenDayManager.openPackage(sell)
                                            } else if (sell.isHoliday()) {
                                                HolidayManager.openPackage(sell)
                                            } else if (sell.isTower()) {
                                                TowerManager.openPackage(sell)
                                            } else if (sell.isMonthCard()) {
                                                MonthCardManager.openPackage(sell)
                                            } else if (sell.isLotteryGift()) {
                                                LotteryManager.openPackage(sell)
                                            } else {
                                                SellManager.openSellChest(sell)
                                            }
                                        }
                                    })
                        } else {
                            if (sell.desc == "0") {
                                AppWrapper.getStringKmp(Res.string.cant_get_yet).toast()
                            } else {
                                if (LoginManager.instance.canShowAifadian()) {
//                                    val uri: Uri = Uri.parse(sell.desc)
//                                    val intent = Intent(Intent.ACTION_VIEW, uri)
//                                    ContextCompat.startActivity(
//                                        GameApp.instance.activity,
//                                        intent,
//                                        Bundle()
//                                    )
                                } else {
                                    AppWrapper.getStringKmp(Res.string.cant_get_yet).toast()
                                }
                            }
                        }
                    }
                } else {
                    Dialogs.alertDialog.value =
                        CommonAlert(
                            title = AppWrapper.getStringKmp(Res.string.confirm_buy),
                            content = AppWrapper.getStringKmp(Res.string.confirm_buy2) + sell.name + "?",
                            onConfirm = {
                                AppWrapper.globalScope.launch(Dispatchers.Main) {
                                    if (sell.isNewTaskPackage()) {
                                        SevenDayManager.openPackage(sell)
                                    } else if (sell.isHoliday()) {
                                        HolidayManager.openPackage(sell)
                                    } else if (sell.isTower()) {
                                        TowerManager.openPackage(sell)
                                    } else if (sell.isMonthCard()) {
                                        MonthCardManager.openPackage(sell)
                                    } else if (sell.isLotteryGift()) {
                                        LotteryManager.openPackage(sell)
                                    } else {
                                        SellManager.openSellChest(sell)
                                    }
                                }
                            })
                }
            }
        })
}