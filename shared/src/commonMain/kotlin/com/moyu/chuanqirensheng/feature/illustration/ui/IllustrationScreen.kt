package com.moyu.chuanqirensheng.feature.illustration.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import com.moyu.core.AppWrapper
import shared.generated.resources.Res
import shared.generated.resources.illustation_sheet1
import shared.generated.resources.illustation_sheet2
import shared.generated.resources.illustation_sheet3
import shared.generated.resources.illustation_sheet4
import shared.generated.resources.illustation_sheet5

val illustrationListTabItems = mutableStateOf(
    listOf(
        AppWrapper.getStringKmp(Res.string.illustation_sheet1),
        AppWrapper.getStringKmp(Res.string.illustation_sheet2),
        AppWrapper.getStringKmp(Res.string.illustation_sheet3),
        AppWrapper.getStringKmp(Res.string.illustation_sheet4),
        AppWrapper.getStringKmp(Res.string.illustation_sheet5),
    )
)

@Composable
fun IllustrationScreen() {
//    val pagerState = rememberPagerState {
//        illustrationListTabItems.value.size
//    }
//    GameBackground(title = stringResource(Res.string.skill_illustration)) {
//        Column(horizontalAlignment = Alignment.CenterHorizontally) {
//            HorizontalPager(
//                modifier = Modifier
//                    .weight(1f)
//                    .fillMaxWidth(),
//                state = pagerState,
//            ) { page ->
//                when (page) {
//                    0 -> IllustrationAllyPage(repo.gameCore.getAllyPool().filter { it.star == 0 })
//                    1 -> IllustrationSkillPage(
//                        repo.gameCore.getSkillPool().filter { it.isBattleTree() }, GameIllustrationManager.unlockSkill1)
//
//                    2 -> IllustrationSkillPage(
//                        repo.gameCore.getSkillPool().filter { it.isJinNang() }, GameIllustrationManager.unlockSkill2)
//
//                    3 -> IllustrationSkillPage(
//                        repo.gameCore.getSkillPool().filter { it.isTalentSpecial() }, GameIllustrationManager.unlockSkill3)
//
//                    else -> IllustrationSkillPage(
//                        repo.gameCore.getSkillPool().filter { it.isZhenLing() }, GameIllustrationManager.unlockSkill4)
//                }
//            }
//            NavigationTab(modifier = Modifier.graphicsLayer {
//                translationY = -padding6.toPx()
//            }, pagerState, illustrationListTabItems.value)
//        }
//    }
}