package com.moyu.chuanqirensheng.feature.continuegame

import com.moyu.chuanqirensheng.logic.battle.NO_TITLE
import com.moyu.core.model.Ally
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.Equipment
import com.moyu.core.model.Event
import com.moyu.core.model.Title
import com.moyu.core.model.property.AdventureProps
import com.moyu.core.model.property.EMPTY_ADV_PROPS
import com.moyu.core.model.property.EMPTY_PROPERTY
import com.moyu.core.model.property.Property
import com.moyu.core.model.role.Role
import com.moyu.core.model.skill.Skill
import kotlinx.serialization.Serializable


@Serializable
data class ContinueData(
    val you: Role = Role(),
    val usedEvents: List<Event> = emptyList(),
    val winEvents: List<Event> = emptyList(),
    val loseEvents: List<Event> = emptyList(),
    val selectionEvents: List<Event> = emptyList(),
    val records: DetailProgressData = DetailProgressData(),
    val skillGameData: List<Skill> = emptyList(),
    val allyGameData: List<Ally> = emptyList(),
    val equipGameData: List<Equipment> = emptyList(),
    val adventureProps: AdventureProps = EMPTY_ADV_PROPS,
    val battleProp: Property = EMPTY_PROPERTY,
    val resources: List<Int> = EMPTY_RESOURCES,
    val yourExp: Int = 0,
    val yourTitle: Title = NO_TITLE,
    val masterProp: Property = EMPTY_PROPERTY,
    val battleSkillPropMap: Map<Int, Property> = emptyMap(),
    val troopSkills: List<Skill> = emptyList(),
    val battleEnchants: List<Skill> = emptyList(),
    val battleRaceProps: List<Property> = mutableListOf(
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
        EMPTY_PROPERTY,
    ),
)