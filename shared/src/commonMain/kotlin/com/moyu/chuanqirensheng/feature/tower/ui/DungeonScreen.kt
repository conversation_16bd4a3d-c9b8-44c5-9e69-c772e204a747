package com.moyu.chuanqirensheng.feature.tower.ui

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.activities.ActivityItem
import com.moyu.chuanqirensheng.feature.pvp.Pvp2Manager
import com.moyu.chuanqirensheng.feature.pvp.PvpManager
import com.moyu.chuanqirensheng.feature.router.PVP2_SCREEN
import com.moyu.chuanqirensheng.feature.router.PVP_SCREEN
import com.moyu.chuanqirensheng.feature.router.TOWER_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.feature.tower.TowerManager
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_PVP2
import com.moyu.chuanqirensheng.feature.unlock.UNLOCK_TOWER
import com.moyu.chuanqirensheng.feature.unlock.UnlockManager
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.ui.theme.padding10
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.challenge
import shared.generated.resources.pvp
import shared.generated.resources.pvp2
import shared.generated.resources.tower_mode

val dungeonItems = listOf(
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.tower_mode) }, route = {
            goto(TOWER_SCREEN)
        },
        frame = "tower_frame",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_TOWER).desc,
        unlock = {
            TowerManager.unlocked()
        },
        red = {
            TowerManager.hasRed()
        }
    ),
    ActivityItem(
        name = { AppWrapper.getStringKmp(Res.string.pvp) },
        route = { goto(PVP_SCREEN) },
        frame = "pvp_label",
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_PVP).desc,
        unlock = {
            PvpManager.unlocked()
        },
        red = {
            PvpManager.hasRedAll()
        }
    ),
    ActivityItem(name = { AppWrapper.getStringKmp(Res.string.pvp2) },
        route = { goto(PVP2_SCREEN) },
        lockedToast = repo.gameCore.getUnlockById(UNLOCK_PVP2).desc,
        frame = "pvp2_frame",
        unlock = {
            Pvp2Manager.unlocked()
        },
        red = {
            Pvp2Manager.hasRedAll()
        }
    ),
)

@Composable
fun DungeonScreen() {
    GameBackground(title = stringResource(Res.string.challenge)) {
        Column(
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(padding10),
            modifier = Modifier
                .fillMaxSize()
                .padding(vertical = padding10, horizontal = padding10)
                .verticalScroll(rememberScrollState())
        ) {
            dungeonItems.forEach {
                ActivityItem(
                    moreItem = it,
                )
            }
        }
    }
}