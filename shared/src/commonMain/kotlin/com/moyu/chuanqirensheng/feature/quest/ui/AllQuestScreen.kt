package com.moyu.chuanqirensheng.feature.quest.ui

import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.graphicsLayer
import com.moyu.chuanqirensheng.feature.quest.QuestManager
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.common.NavigationTab
import com.moyu.chuanqirensheng.ui.theme.padding6
import com.moyu.core.AppWrapper
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.one_time_quest
import shared.generated.resources.quest
import shared.generated.resources.routine_quest


val questListTabItems = mutableStateOf(
        listOf(
            AppWrapper.getStringKmp(Res.string.routine_quest),
            AppWrapper.getStringKmp(Res.string.one_time_quest),
        )
    )

@Composable
fun QuestAllScreen() {
    val questPagerState = rememberPagerState{
        questListTabItems.value.size
    }
    GameBackground(title = stringResource(Res.string.quest)) {
        Column(horizontalAlignment = Alignment.CenterHorizontally) {
            HorizontalPager(
                modifier = Modifier.weight(1f).fillMaxWidth(),
                state = questPagerState,
            ) { page ->
                when (page) {
                    0 -> DailyQuestScreen()
                    else -> OneTimeQuestScreen()
                }
            }
            NavigationTab(modifier = Modifier.graphicsLayer {
                translationY = -padding6.toPx()
            }, questPagerState, questListTabItems.value, listOf(QuestManager.dailyTasks.any {
                QuestManager.getTaskDoneFlow(it)
                        && !it.opened
            }, QuestManager.oneTimeTasks.filter { !it.isEndingTask() }.any {
                QuestManager.getTaskDoneFlow(it)
                        && !it.opened
            }))
        }
    }
}