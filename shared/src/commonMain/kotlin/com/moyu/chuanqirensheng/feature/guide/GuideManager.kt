package com.moyu.chuanqirensheng.feature.guide

import androidx.compose.runtime.mutableIntStateOf
import androidx.compose.runtime.mutableStateOf
import com.moyu.chuanqirensheng.sub.anticheat.GuardedB
import com.moyu.chuanqirensheng.sub.datastore.KEY_NON_BATTLE_DONE

const val TALENT_GUIDE_START = 20
const val BATTLE_GUIDE_START = 10
const val EVENT_GUIDE_START = 1

object GuideManager {
    val guideIndex = mutableIntStateOf(0)
    val showGuide = mutableStateOf(false)
    val nonBattleEventShowed = GuardedB(KEY_NON_BATTLE_DONE)

    fun canBack(): Boolean {
        return !showGuide.value
    }

    fun pauseGame(): Boolean {
        return showGuide.value
    }

    fun showFirstGuide() {
        if (guideIndex.intValue == 0) {
            showGuide.value = true
        }
    }
}