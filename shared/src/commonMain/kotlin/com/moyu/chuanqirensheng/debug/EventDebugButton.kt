package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ExperimentalLayoutApi
import androidx.compose.foundation.layout.FlowRow
import androidx.compose.foundation.layout.FlowRowOverflow
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.logic.award.AwardManager
import com.moyu.chuanqirensheng.logic.battle.BattleManager
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.indexToResourceName
import com.moyu.chuanqirensheng.platform.isReleasePackage
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.button.ButtonSize
import com.moyu.chuanqirensheng.screen.button.ButtonStyle
import com.moyu.chuanqirensheng.screen.button.GameButton
import com.moyu.chuanqirensheng.screen.common.CardSize
import com.moyu.chuanqirensheng.screen.common.DecorateTextField
import com.moyu.chuanqirensheng.screen.common.getTextStyle
import com.moyu.chuanqirensheng.screen.skill.SingleEquipView
import com.moyu.chuanqirensheng.screen.skill.SingleSkillView
import com.moyu.chuanqirensheng.ui.theme.W50
import com.moyu.chuanqirensheng.ui.theme.textFieldHeight
import com.moyu.core.AppWrapper
import com.moyu.core.model.Award
import com.moyu.core.model.EMPTY_RESOURCES
import com.moyu.core.model.Event
import com.moyu.core.model.skill.isAdvBuilding
import com.moyu.core.model.skill.isAdvForAlly
import com.moyu.core.model.skill.isAdvInGame
import com.moyu.core.model.skill.isMagic
import com.moyu.core.model.skill.isProfession
import com.moyu.core.model.skill.isTalentAdv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.main_line

@Composable
fun EventIdTag(modifier: Modifier, event: Event, cardSize: CardSize) {
    if (!isReleasePackage()) {
        Column(modifier = modifier) {
            if (event.isMainLine == 1) {
                Text(
                    text = stringResource(Res.string.main_line), style = cardSize.getTextStyle()
                )
            }
            Text(
                text = event.id.toString(), style = cardSize.getTextStyle()
            )
        }
    }
}

@OptIn(ExperimentalLayoutApi::class)
@Composable
fun EventDebugButton(modifier: Modifier) {
    val show = remember {
        mutableStateOf(false)
    }
    val showJinNang = remember {
        mutableStateOf(false)
    }
    val showPolicy = remember {
        mutableStateOf(false)
    }
    val showBinFu = remember {
        mutableStateOf(false)
    }
    val showZhanJiShu = remember {
        mutableStateOf(false)
    }
    val showTalent = remember {
        mutableStateOf(false)
    }
    val showSpecial = remember {
        mutableStateOf(false)
    }
    val showEquip = remember {
        mutableStateOf(false)
    }
    FlowRow(
        modifier = modifier
            .background(W50)
            .verticalScroll(rememberScrollState()),
        overflow = FlowRowOverflow.Visible,
    ) {
        val text = remember {
            mutableStateOf("")
        }

        if (!isReleasePackage()) {
            GameButton(text = "局内调试") {
                show.value = !show.value
            }
        }

        if (show.value) {
            GameButton(text = "跳过1天") {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, 1)
                }
            }
            GameButton(text = "跳过4天") {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, 4)
                }
            }
            GameButton(text = "跳过10天") {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, 10)
                }
            }
            GameButton(text = "跳过50天") {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, 50)
                }
            }
            GameButton(text = "回滚1天") {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, -1)
                }
            }
            GameButton(text = "回滚30天") {
                AppWrapper.globalScope.launch(Dispatchers.Main) {
                    EventManager.gotoNextEvent(event = null, true, -30)
                }
            }
            GameButton(text = "经验1000") {
                 AppWrapper.globalScope.launch(Dispatchers.Main) {
                    AwardManager.gainAward(award = Award(exp = 1000))
                }
            }
            GameButton(text = "经验10000") {
                 AppWrapper.globalScope.launch(Dispatchers.Main) {
                    AwardManager.gainAward(award = Award(exp = 10000))
                }
            }
            (0..7).forEach {
                GameButton(text = "${it.indexToResourceName()}5000") {
                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                        AwardManager.gainAward(award = Award(resources = EMPTY_RESOURCES.toMutableList().apply {
                            // 资源index=0 5000
                            this[it] = 5000
                        }))
                    }
                }
            }

            GameButton(text = "所有盟友掉血30%") {
                 AppWrapper.globalScope.launch(Dispatchers.Main) {
                    BattleManager.getGameAllies().forEach {
                        BattleManager.hurtAllyInGame(it, 30)
                    }
                }
            }
            (1..7).forEach { star ->
                GameButton(text = "${star}阶盟友") {
                    Award(allies = repo.gameCore.getAllyPool().filter { it.star == 1 && it.quality == star && !it.isHero() }).let {
                         AppWrapper.globalScope.launch(Dispatchers.Main) {
                            AwardManager.gainAward(award = it)
                        }
                    }
                }
            }
            GameButton(text = "枪兵*1000") {
                Award(allies = listOf(repo.gameCore.getAllyPool().first().copy(num = 1000))).let {
                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                        AwardManager.gainAward(award = it)
                    }
                }
            }
            GameButton(text = "显示魔法") {
                showJinNang.value = !showJinNang.value
            }
            if (showJinNang.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isMagic() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示专业") {
                showPolicy.value = !showPolicy.value
            }
            if (showPolicy.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isProfession() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示探索") {
                showBinFu.value = !showBinFu.value
            }
            if (showBinFu.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isAdvInGame() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示天赋") {
                showZhanJiShu.value = !showZhanJiShu.value
            }
            if (showZhanJiShu.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isTalentAdv() && it.level == 1 }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示建筑") {
                showTalent.value = !showTalent.value
            }
            if (showTalent.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isAdvBuilding() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainSkillInGame(target = it)
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示特长") {
                showSpecial.value = !showSpecial.value
            }
            if (showSpecial.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getSkillPool().filter { it.isAdvForAlly() }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleSkillView(skill = it, showStars = false)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.you.value =  BattleManager.you.value.apply {
                                            learnSkill(it, roleIdentifier)
                                        }
                                    }
                                })
                        }
                    }
            }
            GameButton(text = "显示装备") {
                showEquip.value = !showEquip.value
            }
            if (showEquip.value) {
                DecorateTextField(modifier = Modifier.fillMaxWidth().height(textFieldHeight), text.value) {
                    text.value = it
                }
                repo.gameCore.getEquipPool().filter { it.star == 1 }
                    .filter { it.name.contains(text.value) }.forEach {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            SingleEquipView(equipment = it)
                            GameButton(
                                buttonSize = ButtonSize.Small,
                                buttonStyle = ButtonStyle.Orange,
                                text = "学习",
                                onClick = {
                                     AppWrapper.globalScope.launch(Dispatchers.Main) {
                                        BattleManager.gainEquip(it)
                                    }
                                })
                        }
                    }
            }
        }
    }
}