package com.moyu.chuanqirensheng.debug

import androidx.compose.foundation.layout.padding
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import com.moyu.chuanqirensheng.feature.router.DEBUG_SCREEN
import com.moyu.chuanqirensheng.feature.router.goto
import com.moyu.chuanqirensheng.platform.isReleasePackage
import com.moyu.chuanqirensheng.repository.repo
import com.moyu.chuanqirensheng.screen.battle.BattleFieldLayout
import com.moyu.chuanqirensheng.screen.common.GameBackground
import com.moyu.chuanqirensheng.screen.setting.SettingColumn
import com.moyu.chuanqirensheng.screen.setting.settingBattleItems
import com.moyu.chuanqirensheng.ui.theme.padding6

@Composable
fun DebugBattleScreen() {
    if (!isReleasePackage()) {
        BackPressHandler {
            repo.battle.value.terminate()
            repo.onGameOver()
            goto(DEBUG_SCREEN)
        }
        GameBackground(
            showCloseIcon = true,
            showPreviewIcon = false,
            gapStatusBar = false,
        ) {
            BattleFieldLayout(repo.battleRoles)
            SettingColumn(
                Modifier
                    .align(Alignment.CenterStart)
                    .padding(start = padding6), settingBattleItems
            )
        }
    }
}