package com.moyu.chuanqirensheng.logic.event

import com.moyu.chuanqirensheng.feature.difficult.DifficultManager
import com.moyu.core.logic.enemy.DefaultRoleCreator
import com.moyu.core.logic.identifier.Identifier
import com.moyu.core.model.Event
import com.moyu.core.model.Race
import com.moyu.core.model.Tower
import com.moyu.core.model.role.Role

fun createRole(race: Race, event: Event): Role {
    val difficultProperty = DifficultManager.getSelected().toProperty()
    val diffProperty = event.getDiffProperty()
    return DefaultRoleCreator.create(
        race,
        diffProperty + difficultProperty,
        emptyList(),
        Identifier.enemy(name = race.name)
    )
}

fun createTowerRole(race: Race, tower: Tower): Role {
    val difficultProperty = DifficultManager.getSelected().toProperty()
    val diffProperty = tower.getDiffProperty()
    return DefaultRoleCreator.create(
        race,
        diffProperty,
        emptyList(),
        Identifier.enemy(name = race.name)
    )
}