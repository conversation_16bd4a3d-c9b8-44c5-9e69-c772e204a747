package com.moyu.chuanqirensheng.logic.event.detail

import androidx.compose.runtime.Composable
import com.moyu.chuanqirensheng.logic.event.EventManager
import com.moyu.chuanqirensheng.logic.event.PlayHandler
import com.moyu.core.model.Event

class NonePlayHandler(
    override val skipWin: Boolean = true,
    override val playId: Int = 0
): PlayHandler() {

    @Composable
    override fun Layout(event: Event) { }

    override fun onEventSelect(event: Event) {
        EventManager.doEventResult(event, true)
    }
}