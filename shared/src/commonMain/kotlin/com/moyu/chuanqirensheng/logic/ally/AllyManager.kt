package com.moyu.chuanqirensheng.logic.ally

import androidx.compose.runtime.mutableStateListOf
import com.moyu.chuanqirensheng.application.Dialogs
import com.moyu.chuanqirensheng.application.toast
import com.moyu.chuanqirensheng.feature.quest.onTaskStarUp
import com.moyu.chuanqirensheng.logic.basic.BasicItemHolder
import com.moyu.chuanqirensheng.logic.basic.ItemHolder
import com.moyu.chuanqirensheng.screen.dialog.CommonAlert
import com.moyu.chuanqirensheng.screen.effect.dialogEffectState
import com.moyu.chuanqirensheng.screen.effect.restartEffect
import com.moyu.chuanqirensheng.screen.effect.starUpEffect
import com.moyu.chuanqirensheng.sub.datastore.KEY_ALLIES
import com.moyu.core.AppWrapper
import com.moyu.core.GameCore
import com.moyu.core.model.Ally
import com.moyu.core.music.SoundEffect
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.jetbrains.compose.resources.getString
import org.jetbrains.compose.resources.stringResource
import shared.generated.resources.Res
import shared.generated.resources.already_max_star_tips
import shared.generated.resources.card_not_enough
import shared.generated.resources.star_up_all_info
import shared.generated.resources.star_up_all_tips
import shared.generated.resources.star_up_name_tips
import shared.generated.resources.upgrade_all_result_content
import shared.generated.resources.upgrade_all_result_title
import kotlin.math.max

class AllyManager(
    private val holder: ItemHolder<Ally> = BasicItemHolder(saveKey = KEY_ALLIES,
        elementSerializer = Ally.serializer(),
        data = mutableStateListOf(),
        sameGroup = { item1, item2 -> item1.mainId == item2.mainId },
        increase = { current, add ->
            current.copy(
                num = current.num + add.num,
                new = add.new,
                id = if (add.star > current.star) add.id else current.id,
                starUpNum = if (add.star > current.star) add.starUpNum else current.starUpNum,
                starUpRes = if (add.star > current.star) add.starUpRes else current.starUpRes,
                star = max(current.star, add.star)
            )
        })
) : ItemHolder<Ally> by holder {

    override fun gain(value: Ally) {
        // todo 注意，如果是首次获得，数量要-1，本体扣除1
        data.indexOfFirst { sameGroup(value, it) }.takeIf { it >= 0 }?.let {
            data[it] = increase(data[it], value)
        } ?: run {
            data.add(value.copy(num = value.num - 1))
        }
        save()
    }

    // data修改都需要确保主线程
    fun selectToGame(target: Ally) {
        GameCore.instance.onBattleEffect(SoundEffect.EquipItem)
        // 新增逻辑
        if (target.isHero() && !target.selected) {
            data.filter { it.isHero() && it.selected }.forEach { deselectAlly ->
                data.indexOfFirst { it.id == deselectAlly.id }.takeIf { it != -1 }?.let {
                    data[it] = data[it].switchSelect()
                }
            }
        }
        data.indexOfFirst { it.id == target.id }.takeIf { it != -1 }?.let {
            data[it] = data[it].switchSelect()
            save()
        }
    }

    // data修改都需要确保主线程
    // 升星逻辑改了，有基底概念，所以要-1
    fun upgrade(ally: Ally): Ally? {
//        if (AwardManager.diamond.value < ally.starUpRes) {
//            AppWrapper.getStringKmp(Res.string.diamond_not_enough).toast()
//        } else
        if (ally.starUpNum == 0) {
            AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
        } else if (ally.starUpNum > ally.num) {
            AppWrapper.getStringKmp(Res.string.card_not_enough).toast()
        } else if (ally.star >= ally.starLimit) {
            AppWrapper.getStringKmp(Res.string.already_max_star_tips).toast()
        } else {
            GameCore.instance.onBattleEffect(SoundEffect.UpgradeItem)
            restartEffect(dialogEffectState, starUpEffect)
//            AwardManager.gainDiamond(-ally.starUpRes)
            data.indexOfFirst { it.id == ally.id }.takeIf { it != -1 }?.let {
                onTaskStarUp(data[it], data[it].star + 1)
                data[it] = data[it].copy(num = data[it].num - ally.starUpNum).starUp()
                save()
                return data[it]
            }
        }
        return null
    }

    suspend fun starUpAll(isHero: Boolean) {
        val upgradeInfo = mutableListOf<String>()
        var upgradeCount = 0
        val upgradeResultInfo = mutableListOf<String>()
        Dialogs.commonBlockDialog.value = AppWrapper.getStringKmp(Res.string.star_up_all_tips)
        repeat(data.filter { isHero == it.isHero() }.size) { index ->
            var upgraded = true
            var needCount = false
            val startLevel = data.filter { isHero == it.isHero() }[index].star
            upgradeInfo.add(
                0, runBlocking { getString(Res.string.star_up_name_tips, data.filter { isHero == it.isHero() }[index].name) }
            )
            while (upgraded) {
                val ally = data.filter { isHero == it.isHero() }[index]
                when {
                    ally.starUpNum == 0 -> {
                        upgraded = false
                    }

                    ally.starUpNum > ally.num -> {
                        upgraded = false
                    }

                    ally.star >= ally.starLimit -> {
                        upgraded = false
                    }

                    else -> {
                        data.indexOfFirst { it.id == ally.id }.takeIf { it != -1 }?.let {
                            upgradeInfo.add(
                                0, runBlocking { getString(
                                        Res.string.star_up_all_info,
                                    data[it].name,
                                    data[it].star,
                                    data[it].star + 1
                                ) }
                            )
                            onTaskStarUp(data[it], data[it].star + 1)
                            data[it] = data[it].copy(num = data[it].num - ally.starUpNum).starUp()
                            needCount = true
                        }
                    }
                }
            }
            if (needCount) {
                upgradeCount += 1
                upgradeResultInfo.add(
                    runBlocking { getString(
                        Res.string.star_up_all_info,
                        data.filter { isHero == it.isHero() }[index].name,
                        startLevel,
                        data.filter { isHero == it.isHero() }[index].star
                    ) }
                )
            }
            Dialogs.commonBlockDialog.value =
                AppWrapper.getStringKmp(Res.string.star_up_all_tips) + "\n" + upgradeInfo.joinToString(
                    "\n"
                )
            delay(20)
        }
        GameCore.instance.onBattleEffect(SoundEffect.UpgradeItem)
        save()
        Dialogs.alertDialog.value = CommonAlert(
            title = AppWrapper.getStringKmp(Res.string.upgrade_all_result_title),
            content = getString(Res.string.upgrade_all_result_content, upgradeCount) + "\n" + upgradeResultInfo.joinToString(
                "\n"
            ),
            onlyConfirm = true,
        )
        delay(500)
        Dialogs.commonBlockDialog.value = null
    }
}
