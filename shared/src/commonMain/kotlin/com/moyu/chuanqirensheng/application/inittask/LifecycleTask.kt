package com.moyu.chuanqirensheng.application.inittask

import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.moyu.chuanqirensheng.media.MusicManager
import com.moyu.chuanqirensheng.media.playerMusicByScreen
import com.moyu.chuanqirensheng.sub.job.Context
import com.moyu.chuanqirensheng.sub.job.JobContent
import com.moyu.core.AppWrapper
import kotlinx.coroutines.launch

/**
 * 另外音乐会根据前后台处理
 */
class LifecycleTask : JobContent<Context> {
    private val lifecycleObserver = MusicLifecycleObserver()

    override fun execute(context: Context) {
        context.lifecycle.lifecycle.addObserver(lifecycleObserver)
    }

}

class MusicLifecycleObserver : DefaultLifecycleObserver {
    private var activityAccount = 0

    override fun onCreate(owner: LifecycleOwner) {
        super.onCreate(owner)
    }

    override fun onStart(owner: LifecycleOwner) {
        super.onStart(owner)

        if (activityAccount == 0) {
            MusicManager.muteByBackGround(false)
        }
        AppWrapper.isForeground = true
        activityAccount++
    }

    override fun onResume(owner: LifecycleOwner) {
        super.onResume(owner)
        playerMusicByScreen()
    }

    override fun onPause(owner: LifecycleOwner) {
        super.onPause(owner)
    }

    override fun onStop(owner: LifecycleOwner) {
        super.onStop(owner)

        activityAccount--
        if (activityAccount == 0) {
            AppWrapper.isForeground = false
            AppWrapper.globalScope.launch {
                MusicManager.muteByBackGround(true)
                MusicManager.stopAll()
            }
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        super.onDestroy(owner)
    }
}