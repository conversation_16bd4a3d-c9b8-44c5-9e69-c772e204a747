package com.moyu.chuanqirensheng.platform

import androidx.compose.runtime.MutableState
import androidx.compose.ui.graphics.ImageBitmap
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.eygraber.uri.Uri
import com.moyu.chuanqirensheng.feature.rank.RankData
import com.moyu.chuanqirensheng.getSystemElapsedBootTimeMillis
import com.moyu.chuanqirensheng.iosBottomHeightInDp
import com.moyu.chuanqirensheng.media.MusicPlayerInterface
import com.moyu.chuanqirensheng.platform.ios.GameDefaultSdkProcessor
import com.moyu.chuanqirensheng.platform.ios.MusicPlayer
import com.moyu.chuanqirensheng.platform.ios.ReportManager
//import com.moyu.chuanqirensheng.platform.ios.iosLoadLocalFile
import com.moyu.chuanqirensheng.sub.bill.PayClientData
import com.moyu.chuanqirensheng.sub.datastore.KEY_NEED_SHOW_PRIVACY
import com.moyu.chuanqirensheng.sub.datastore.KEY_VERIFIED
import com.moyu.chuanqirensheng.sub.datastore.getBooleanFlowByKey
import com.moyu.chuanqirensheng.sub.loginsdk.GameSdkProcessor
import com.moyu.chuanqirensheng.sub.report.ReportInterface
import com.moyu.chuanqirensheng.sub.saver.CloudSaver
import com.moyu.core.AppWrapper
import com.moyu.core.model.Sell
import com.moyu.core.util.p_getTimeMillis
import core.generated.resources.Res as CoreRes
import shared.generated.resources.Res
import io.ktor.client.HttpClient
import io.ktor.client.plugins.contentnegotiation.ContentNegotiation
import io.ktor.serialization.kotlinx.json.json
import kotlinx.serialization.json.Json
import org.jetbrains.compose.resources.ExperimentalResourceApi

import platform.UIKit.UIDevice
import io.ktor.client.engine.darwin.*
import kotlinx.cinterop.ExperimentalForeignApi
import platform.Foundation.NSDocumentDirectory
import platform.Foundation.NSFileManager
import platform.Foundation.NSURL
import platform.Foundation.NSUserDomainMask

import com.moyu.chuanqirensheng.iosDecryptBlock
import com.moyu.chuanqirensheng.iosEncryptBlock
import com.moyu.chuanqirensheng.iosIsReleasePackage
import com.moyu.chuanqirensheng.iosPhoneLanguage
import com.moyu.chuanqirensheng.iosPurchaseProduct
import com.moyu.chuanqirensheng.iosScreenDensity
import com.moyu.chuanqirensheng.iosScreenHeightInDp
import com.moyu.chuanqirensheng.iosScreenWidthInDp
import com.moyu.chuanqirensheng.iosSetAppLanguage
import com.moyu.chuanqirensheng.iosStatusBarHeightInDp
import com.moyu.chuanqirensheng.iosVersionCode
import com.moyu.chuanqirensheng.iosVersionName
import com.moyu.chuanqirensheng.platform.ios.AdPlayer
import com.moyu.chuanqirensheng.sub.ad.AdInterface
import com.moyu.chuanqirensheng.util.Platform
import kotlinx.datetime.Instant
import kotlinx.datetime.LocalDateTime
import kotlinx.datetime.TimeZone
import kotlinx.datetime.format
import kotlinx.datetime.format.byUnicodePattern
import kotlinx.datetime.toLocalDateTime
import platform.Foundation.NSCachesDirectory
import platform.StoreKit.SKStoreReviewController
import platform.UIKit.UIApplication
import platform.UIKit.UIPasteboard
import platform.UIKit.endEditing
import platform.posix.exit

actual fun getPlatform(): Platform {
    return Platform.IOS
}

actual fun getSignature(): String {
    return "4b48de895e5ffb24b2665631078db5a7bd8ce454"
}

actual fun aes_encrypt(content: String, password: String): String? {
    return iosEncryptBlock?.invoke(content, password)
}

actual fun aes_decrypt(content: String, password: String): String? {
    return iosDecryptBlock?.invoke(content, password)
}

actual fun getLocalFilePath(item: String): String {
    return getLocalFilePath("files", item)
}

@OptIn(ExperimentalForeignApi::class)
actual fun getSystemFilesPath(): String {
    val documentDirectory: NSURL? = NSFileManager.defaultManager.URLForDirectory(
        directory = NSDocumentDirectory,
        inDomain = NSUserDomainMask,
        appropriateForURL = null,
        create = false,
        error = null,
    )
    return requireNotNull(documentDirectory).path ?: ""
}

@OptIn(ExperimentalForeignApi::class)
actual fun getSystemCacheDirPath(): String {
    val cacheDirectory: NSURL? = NSFileManager.defaultManager.URLForDirectory(
        directory = NSCachesDirectory,
        inDomain = NSUserDomainMask,
        appropriateForURL = null,
        create = false,
        error = null,
    )
    return requireNotNull(cacheDirectory).path ?: ""
}

@OptIn(ExperimentalResourceApi::class)
actual fun getLocalFilePath(path: String, item: String): String {
//    val filePath = NSBundle.mainBundle.pathForResource(item, null)

    // 可以正常工作，但最好不要写死path路径。路径依赖了 KMP Res.getUrl 函数内部的实现，后续可能会被修改
//    val path = "compose-resources/composeResources/hellokmpdemo.shared.generated.resources/files"
//    val filePath = NSBundle.mainBundle.pathForResource("$path/$item", null)

    // 删除scheme file:// 以获取文件的路径
    val fileUrlPath = Res.getUri("${path}/${item}")
    val filePath = fileUrlPath.replace("file://", "")

    return filePath ?: ""
}

actual fun screenWidthInDp(): Float {
    return iosScreenWidthInDp
}

actual fun screenHeightInDp(): Float {
//    return 750f
    return iosScreenHeightInDp
}

actual fun screenDensity(): Float {
//    println("=-== $iosScreenHeightInPixel $iosScreenHeightInDp ${iosScreenHeightInPixel / iosScreenHeightInDp}")
//    return iosScreenHeightInPixel / iosScreenHeightInDp
//    return 2.0f
    return iosScreenDensity
}

actual fun statusBarHeightInDp(): Dp {
    return iosStatusBarHeightInDp.dp
}

actual fun bottomHeightInDp(): Dp {
    return iosBottomHeightInDp.dp
}

actual fun topHeightInDp(): Dp {
    return 50.dp//iosStatusBarHeightInDp.dp
}

actual fun hideKeyboard() {
    UIApplication.sharedApplication.keyWindow?.endEditing(true)
}

actual fun triggerRebirth() {
    killSelf()
}

actual fun killSelf() {
    exit(0)
}

/**
 * 日期格式字符串转换成时间戳
 * @param format 如：yyyy-MM-dd HH:mm:ss
 * @return
 */
// yyyy-MM-dd HH:mm:ss
actual fun date2TimeStamp(timestampMill: Long, format: String?): String {
    var tmpFormat = format
    if (tmpFormat == null) {
        tmpFormat = "yyyy-MM-dd HH:mm:ss"
    }

    val instant = Instant.fromEpochMilliseconds(timestampMill)
    val time = instant.toLocalDateTime(TimeZone.currentSystemDefault())
    val result = time.format(LocalDateTime.Format { byUnicodePattern(tmpFormat) })
    return result
}

actual fun Long.millisToHoursMinutesSeconds(): String {
    val hours = this / 1000 / 60 / 60
    val minutes = this / 1000 / 60 % 60
    val seconds = this / 1000 % 60
    return "${hours.formate2()}:${minutes.formate2()}:${seconds.formate2()}"
}

private fun Long.formate2(): String {
    return this.toString().padStart(2, '0')
}

actual fun getElapsedTimeMillis(): Long {
    return getSystemElapsedBootTimeMillis()
}

actual fun getVersion(): String {
    return iosVersionName
}

actual fun getVersionCode(): Int {
    return iosVersionCode.toInt()
}

actual fun isLite(): Boolean {
    return !isReleasePackage()
}

// 打带有调试功能的包 qudingding
actual fun isReleasePackage(): Boolean {
    return iosIsReleasePackage
}

actual fun serverUrl(): String {
    return "http://43.134.0.148:9795/yuansuqiu/api/v2/"
}

private var gameSdkProcessor: GameSdkProcessor? = null
actual fun gameSdkDefaultProcessor(): GameSdkProcessor {
    if (gameSdkProcessor == null) {
        gameSdkProcessor = GameDefaultSdkProcessor()
    }
    return gameSdkProcessor!!
}

private var httpClient: HttpClient? = null
actual fun createHttpClient(): HttpClient {
    if (httpClient == null) {
        // https://ktor.io/docs/client-engines.html#darwin
        httpClient = HttpClient(Darwin) {
            install(ContentNegotiation) {
                json(Json {
                    ignoreUnknownKeys = true
                    useAlternativeNames = false
                })
            }
        }
    }

    return httpClient!!
}

private var reportManager: ReportInterface? = null
actual fun reportManager(): ReportInterface {
    if (reportManager == null) {
        reportManager = ReportManager()
    }
    return reportManager!!
}

private var musicPlayer: MusicPlayerInterface? = null
actual fun createMusicPlayer(musicVolumeCallback: () -> Float): MusicPlayerInterface {
    if (musicPlayer == null) {
        musicPlayer = MusicPlayer(musicVolumeCallback)
    }
    return musicPlayer!!
}

actual fun openGamePage(uri: Uri) {
//    print("=-== todo: openGamePage $uri")
}

actual fun needPrivacyCheck(): Boolean {
    return false
}

actual fun hasGoogleService(): Boolean {
    return true
}

actual fun platformChannel(): String {
    return "ios"
}

actual fun billPrepay(sell: Sell, function: () -> Unit) {
    iosPurchaseProduct?.let {
//        println("=-== bill sell.id: ${sell.id}, sell.googleItemId: ${sell.googleItemId}")
        if (sell.googleItemId == "0") {
            it(sell.id.toString(), sell.id.toString(), sell.priceDollar, function)
        } else {
            it(sell.id.toString(), sell.googleItemId, sell.priceDollar, function)
        }
    }
}

actual fun antiAddictVerified(): Boolean {
    return getBooleanFlowByKey(
        KEY_VERIFIED, false)
}

actual fun privacyNeedShow(): Boolean {
    return getBooleanFlowByKey(
        KEY_NEED_SHOW_PRIVACY, false)
}

actual fun getLocalLanguage(): String {
    return iosPhoneLanguage
}

actual fun setLocaleLanguage(languageCode: String) {
    iosSetAppLanguage?.let {
        it(languageCode)
    }
}

actual fun getLocalCountry(): String {
    return "usa" // 目前没用
}

actual fun billPayClientDataList(): List<PayClientData> {
    // TODO: 海外不需要实现
    return emptyList<PayClientData>()
}

actual fun billRemovePayClientData(it: PayClientData) {
    // TODO: 海外不需要实现
}

actual fun platformTaskExecute() {
//    BuglyTask().execute(GameApp.instance.gameContext)
//    TTRewardAdTask().execute(GameApp.instance.gameContext)
//    ChannelTask().execute(GameApp.instance.gameContext)
//    RootCheckerTask().execute(GameApp.instance.gameContext)
//    BillingTask().execute(GameApp.instance.gameContext)
}

actual fun getBuildFlavor(): String {
    return "oversea"
}

actual fun p_getfootPrint(): String {
    return "ios"
}

@OptIn(ExperimentalForeignApi::class)
actual fun p_fileRename(oldFileName: String, newFileName: String) {
    val fileManager = NSFileManager.defaultManager
    if (fileManager.fileExistsAtPath(newFileName)) {  // move操作不会覆盖旧文件，需要先移除旧文件
        fileManager.removeItemAtPath(newFileName, null)
    }
    fileManager.moveItemAtPath(oldFileName, newFileName, null)
}

actual fun resetClipboard(text: String) {
    UIPasteboard.generalPasteboard.setString(text)
}

actual fun p_requestInAppReview() {
    SKStoreReviewController.requestReview()
}

actual fun p_getAdImp(): AdInterface {
    return AdPlayer
}

actual fun getHeadBitmap(): ImageBitmap? {
    return null
}

actual fun refreshRankList(type: Int, callback: ((list:List<RankData>) -> Unit)?) {}
actual fun setAchievement(name: String) {}
actual fun setLeaderBoardScore(score: Int, type: Int) {}

actual fun hasBilling(): Boolean {
    return true
}

actual fun isToutiao(): Boolean {
    return  false
}

actual fun isTaptap(): Boolean {
    return false
}
